#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'http://127.0.0.1:54321'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testAuth() {
  console.log('Testing authentication with restored cloud data...')
  
  // Test 1: Check if we can fetch profiles
  console.log('\n1. Testing profile fetch...')
  const { data: profiles, error: profileError } = await supabase
    .from('profiles')
    .select('id, email, first_name, last_name')
    .limit(3)

  if (profileError) {
    console.error('Profile fetch error:', profileError)
  } else {
    console.log('✅ Profiles fetched successfully:', profiles.length, 'profiles')
    profiles.forEach(p => console.log(`  - ${p.first_name} ${p.last_name} (${p.email})`))
  }

  // Test 2: Check if we can fetch teams
  console.log('\n2. Testing teams fetch...')
  const { data: teams, error: teamsError } = await supabase
    .from('teams')
    .select('id, name, owner_id')
    .limit(3)

  if (teamsError) {
    console.error('Teams fetch error:', teamsError)
  } else {
    console.log('✅ Teams fetched successfully:', teams.length, 'teams')
    teams.forEach(t => console.log(`  - ${t.name} (${t.id})`))
  }

  // Test 3: Check specific user that was having issues
  console.log('\n3. Testing specific user profile...')
  const { data: specificProfile, error: specificError } = await supabase
    .from('profiles')
    .select('*')
    .eq('email', '<EMAIL>')
    .single()

  if (specificError) {
    console.error('Specific profile fetch error:', specificError)
  } else {
    console.log('✅ Specific profile found:')
    console.log(`  - ID: ${specificProfile.id}`)
    console.log(`  - Name: ${specificProfile.first_name} ${specificProfile.last_name}`)
    console.log(`  - Email: ${specificProfile.email}`)
    console.log(`  - Role: ${specificProfile.role}`)
  }

  console.log('\n🎉 Database restoration test completed!')
  console.log('\nYou should now be able to login with any of the restored user accounts.')
  console.log('Note: You\'ll need the actual passwords from the production system.')
}

testAuth().catch(console.error)
