#!/usr/bin/env node

/**
 * Test script to verify local authentication and profile loading
 * Run with: node tests/test-local-auth.mjs
 */

import { createClient } from '@supabase/supabase-js'

// Local Supabase configuration
const supabaseUrl = 'http://127.0.0.1:54321'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testLocalAuth() {
  console.log('🧪 Testing Local Authentication and Profile Loading')
  console.log('=' .repeat(60))

  try {
    // Test 0: Create a test user first
    console.log('\n0️⃣ Creating Test User...')
    const testEmail = '<EMAIL>'
    const testPassword = 'testpassword123'

    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          first_name: 'Test',
          last_name: 'User',
          role: 'property_manager'
        }
      }
    })

    if (signUpError && !signUpError.message.includes('already registered')) {
      console.error('❌ Sign up failed:', signUpError.message)
      return
    }

    console.log('✅ Test user created or already exists')

    // Test 1: Sign in with the test user
    console.log('\n1️⃣ Testing Sign In...')
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword
    })
    
    if (authError) {
      console.error('❌ Sign in failed:', authError.message)
      return
    }
    
    console.log('✅ Sign in successful')
    console.log('👤 User ID:', authData.user.id)
    console.log('📧 Email:', authData.user.email)
    
    // Test 2: Fetch user profile
    console.log('\n2️⃣ Testing Profile Fetch...')
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single()
    
    if (profileError) {
      console.error('❌ Profile fetch failed:', profileError.message)
      return
    }
    
    console.log('✅ Profile fetch successful')
    console.log('👤 Name:', profile.first_name, profile.last_name)
    console.log('🎭 Role:', profile.role)
    console.log('📧 Profile Email:', profile.email)
    
    // Test 3: Test session persistence
    console.log('\n3️⃣ Testing Session...')
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      console.error('❌ Session fetch failed:', sessionError.message)
      return
    }
    
    if (session) {
      console.log('✅ Session is active')
      console.log('🔑 Session User ID:', session.user.id)
    } else {
      console.log('❌ No active session')
    }
    
    // Test 4: Sign out
    console.log('\n4️⃣ Testing Sign Out...')
    const { error: signOutError } = await supabase.auth.signOut()
    
    if (signOutError) {
      console.error('❌ Sign out failed:', signOutError.message)
      return
    }
    
    console.log('✅ Sign out successful')
    
    console.log('\n🎉 All tests passed! Local auth and profile loading is working correctly.')
    
  } catch (error) {
    console.error('💥 Test failed with exception:', error.message)
  }
}

// Run the test
testLocalAuth()
