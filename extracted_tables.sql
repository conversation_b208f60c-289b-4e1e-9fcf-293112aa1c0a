-- Extracted table definitions from schema backup

-- Table: profiles
CREATE TABLE public.profiles (
    id uuid NOT NULL,
    first_name text,
    last_name text,
    email text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    avatar_url text,
    is_super_admin boolean DEFAULT false NOT NULL,
    role public.user_role DEFAULT 'property_manager'::public.user_role NOT NULL
);

-- Table: teams
CREATE TABLE public.teams (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    owner_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Table: team_members
CREATE TABLE public.team_members (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    team_id uuid NOT NULL,
    user_id uuid NOT NULL,
    added_by uuid NOT NULL,
    status text DEFAULT 'pending'::text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Table: team_properties
CREATE TABLE public.team_properties (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    team_id uuid NOT NULL,
    property_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Table: properties
CREATE TABLE public.properties (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    address text NOT NULL,
    city text NOT NULL,
    state text NOT NULL,
    zip text NOT NULL,
    image_url text,
    user_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    bedrooms integer DEFAULT 1,
    bathrooms integer DEFAULT 1,
    budget numeric,
    ical_url text,
    next_booking text,
    collections jsonb,
    last_ical_sync text,
    is_occupied boolean DEFAULT false,
    current_checkout text,
    next_checkin_date text,
    next_checkin_formatted text,
    team_id uuid,
    timezone text DEFAULT 'America/Los_Angeles'::text,
    check_in_time time without time zone DEFAULT '15:00:00'::time without time zone,
    check_out_time time without time zone DEFAULT '11:00:00'::time without time zone
);

-- Table: maintenance_tasks
CREATE TABLE public.maintenance_tasks (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    property_id uuid,
    property_name text NOT NULL,
    title text NOT NULL,
    description text,
    status text DEFAULT 'new'::text NOT NULL,
    severity text DEFAULT 'medium'::text NOT NULL,
    due_date text,
    assigned_to text,
    provider_id uuid,
    provider_email text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    team_id uuid,
    is_recurring boolean DEFAULT false,
    recurrence_interval_days integer,
    parent_task_id uuid,
    next_due_date timestamp with time zone,
    recurrence_count integer DEFAULT 0,
    max_recurrences integer,
    completed_at timestamp with time zone,
    email_notification_sent boolean DEFAULT false,
    email_notification_sent_at timestamp with time zone
);

-- Table: damage_reports
CREATE TABLE public.damage_reports (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    property_id uuid NOT NULL,
    title text NOT NULL,
    description text NOT NULL,
    status text DEFAULT 'open'::text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    user_id uuid NOT NULL,
    provider_id uuid,
    platform text,
    team_id uuid
);

-- Table: inventory_items
CREATE TABLE public.inventory_items (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    property_id uuid NOT NULL,
    collection_id uuid,
    quantity integer DEFAULT 0 NOT NULL,
    min_quantity integer DEFAULT 1 NOT NULL,
    price numeric(10,2),
    amazon_url text,
    walmart_url text,
    asin text,
    walmart_item_id text,
    user_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    collection text NOT NULL,
    image_url text,
    team_id uuid
);

-- Table: purchase_orders
CREATE TABLE public.purchase_orders (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    property_id uuid NOT NULL,
    status public.po_status DEFAULT 'pending'::public.po_status NOT NULL,
    total_price numeric(10,2),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    user_id uuid NOT NULL,
    notes text,
    is_archived boolean DEFAULT false NOT NULL,
    team_id uuid
);

-- Table: purchase_order_items
CREATE TABLE public.purchase_order_items (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    purchase_order_id uuid NOT NULL,
    inventory_item_id uuid,
    item_name text NOT NULL,
    quantity integer NOT NULL,
    price numeric(10,2),
    amazon_url text,
    walmart_url text,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Table: invitations
CREATE TABLE public.invitations (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    email text NOT NULL,
    team_id uuid NOT NULL,
    invited_by uuid NOT NULL,
    role public.user_role NOT NULL,
    token text NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    status text DEFAULT 'pending'::text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Table: team_invitations
CREATE TABLE public.team_invitations (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    team_id uuid NOT NULL,
    email text NOT NULL,
    invited_by uuid NOT NULL,
    role public.user_role NOT NULL,
    token text NOT NULL,
    status text DEFAULT 'pending'::text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    team_name text,
    accepted_at timestamp with time zone
);

-- Table: user_permissions
CREATE TABLE public.user_permissions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    team_id uuid,
    permission public.permission_type NOT NULL,
    enabled boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Table: user_preferences
CREATE TABLE public.user_preferences (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    onboarding_state jsonb DEFAULT '{"hasSeenDamagesTutorial": false, "hasSeenDashboardTutorial": false, "hasSeenInventoryTutorial": false, "hasSeenPropertiesTutorial": false, "hasSeenMaintenanceTutorial": false}'::jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Table: user_settings
CREATE TABLE public.user_settings (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    dark_mode boolean DEFAULT false NOT NULL,
    compact_mode boolean DEFAULT false NOT NULL,
    animations boolean DEFAULT true NOT NULL,
    email_notifications boolean DEFAULT true NOT NULL,
    push_notifications boolean DEFAULT false NOT NULL,
    weekly_summary boolean DEFAULT true NOT NULL,
    inventory_alerts boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Table: service_providers
CREATE TABLE public.service_providers (
    id uuid NOT NULL,
    email text NOT NULL,
    first_name text,
    last_name text,
    status text DEFAULT 'active'::text NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Table: maintenance_providers
CREATE TABLE public.maintenance_providers (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    specialty text,
    phone text,
    email text,
    notes text,
    user_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    team_id uuid
);

-- Table: maintenance_requests
CREATE TABLE public.maintenance_requests (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    property_id uuid NOT NULL,
    title text NOT NULL,
    description text NOT NULL,
    status text DEFAULT 'open'::text NOT NULL,
    priority text DEFAULT 'medium'::text NOT NULL,
    provider_id uuid,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    user_id uuid NOT NULL
);

-- Table: property_documents
CREATE TABLE public.property_documents (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    property_id uuid NOT NULL,
    user_id uuid NOT NULL,
    title text NOT NULL,
    content text NOT NULL,
    is_private boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Table: property_files
CREATE TABLE public.property_files (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    property_id uuid NOT NULL,
    user_id uuid NOT NULL,
    filename text NOT NULL,
    file_path text NOT NULL,
    file_type text NOT NULL,
    file_size integer NOT NULL,
    is_private boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    display_name text,
    caption text
);

