#!/usr/bin/env node

/**
 * Download Supabase Edge Functions using the Management API
 * This script retrieves function source code directly from the Supabase API
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// Configuration
const PROJECT_REF = 'pwaeknalhosfwuxkpaet';
const FUNCTIONS_DIR = 'supabase/functions';
const BACKUP_DIR = `.supabase/backups/functions/${new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)}`;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Function to make HTTPS requests
function makeRequest(options) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });
    
    req.on('error', reject);
    req.end();
  });
}

// Get Supabase access token from CLI
async function getAccessToken() {
  return new Promise((resolve, reject) => {
    const { exec } = require('child_process');
    exec('supabase auth token', (error, stdout, stderr) => {
      if (error) {
        reject(new Error('Failed to get Supabase access token. Make sure you are logged in with "supabase login"'));
        return;
      }
      resolve(stdout.trim());
    });
  });
}

// Get list of functions
async function getFunctions(accessToken) {
  const options = {
    hostname: 'api.supabase.com',
    port: 443,
    path: `/v1/projects/${PROJECT_REF}/functions`,
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  };

  const response = await makeRequest(options);
  if (response.status !== 200) {
    throw new Error(`Failed to get functions list: ${response.status}`);
  }
  
  return response.data;
}

// Get function source code (this might not be directly available via API)
async function getFunctionSource(accessToken, functionSlug) {
  // Try to get function details
  const options = {
    hostname: 'api.supabase.com',
    port: 443,
    path: `/v1/projects/${PROJECT_REF}/functions/${functionSlug}`,
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  };

  const response = await makeRequest(options);
  return response;
}

// Create directory structure
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Create backup of existing functions
function backupExistingFunctions() {
  if (fs.existsSync(FUNCTIONS_DIR)) {
    log('📦 Creating backup of existing functions...', 'yellow');
    ensureDir(BACKUP_DIR);
    
    const { execSync } = require('child_process');
    try {
      execSync(`cp -r ${FUNCTIONS_DIR}/* ${BACKUP_DIR}/`, { stdio: 'ignore' });
      log(`✅ Backup created at: ${BACKUP_DIR}`, 'green');
    } catch (error) {
      log('⚠️  No existing functions to backup', 'yellow');
    }
  }
}

// Create a basic function template
function createFunctionTemplate(functionName, functionData) {
  const functionDir = path.join(FUNCTIONS_DIR, functionName);
  ensureDir(functionDir);

  // Create deno.json
  const denoConfig = {
    imports: {
      "supabase": "https://esm.sh/@supabase/supabase-js@2"
    }
  };
  
  fs.writeFileSync(
    path.join(functionDir, 'deno.json'),
    JSON.stringify(denoConfig, null, 2)
  );

  // Create index.ts template
  const template = `// Function: ${functionName}
// Status: Template - Replace with actual production code
// Created: ${new Date().toISOString()}
// Production Function ID: ${functionData.id}
// Version: ${functionData.version}
// JWT Verification: ${functionData.verify_jwt}

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'supabase'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // TODO: Implement actual function logic here
    // This is a template - replace with the actual production code
    
    return new Response(
      JSON.stringify({ 
        message: "Function template created successfully",
        function: "${functionName}",
        note: "Replace this template with actual production code",
        productionInfo: {
          id: "${functionData.id}",
          version: ${functionData.version},
          verifyJwt: ${functionData.verify_jwt}
        }
      }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200
      }
    )
  } catch (error) {
    console.error('Function error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500
      }
    )
  }
})
`;

  fs.writeFileSync(path.join(functionDir, 'index.ts'), template);
}

// Main function
async function main() {
  try {
    log('🚀 Starting Supabase Functions Download via API', 'blue');
    log(`Project: ${PROJECT_REF}`, 'cyan');
    log(`Functions Directory: ${FUNCTIONS_DIR}`, 'cyan');
    log(`Backup Directory: ${BACKUP_DIR}`, 'cyan');
    console.log();

    // Get access token
    log('🔑 Getting access token...', 'yellow');
    const accessToken = await getAccessToken();
    log('✅ Access token obtained', 'green');

    // Backup existing functions
    backupExistingFunctions();

    // Ensure functions directory exists
    ensureDir(FUNCTIONS_DIR);

    // Get functions list
    log('📋 Fetching functions list...', 'yellow');
    const functions = await getFunctions(accessToken);
    log(`✅ Found ${functions.length} functions`, 'green');

    let created = 0;
    let failed = 0;

    // Process each function
    for (const func of functions) {
      try {
        log(`📝 Creating template for: ${func.slug}`, 'yellow');
        
        // Try to get more details about the function
        const details = await getFunctionSource(accessToken, func.slug);
        
        createFunctionTemplate(func.slug, func);
        log(`✅ Created template for: ${func.slug}`, 'green');
        created++;
        
      } catch (error) {
        log(`❌ Failed to create template for: ${func.slug} - ${error.message}`, 'red');
        failed++;
      }
    }

    console.log();
    log('📊 Download Summary', 'blue');
    log(`✅ Successfully created templates: ${created} functions`, 'green');
    log(`❌ Failed: ${failed} functions`, 'red');
    log(`📁 Backup location: ${BACKUP_DIR}`, 'cyan');

    console.log();
    log('🎉 Function template creation completed!', 'green');
    log('Next steps:', 'blue');
    console.log('   1. Review the function templates in:', FUNCTIONS_DIR);
    console.log('   2. Replace templates with actual production code');
    console.log('   3. Test functions locally with: supabase functions serve');
    console.log('   4. Deploy when ready with: supabase functions deploy <function-name>');

  } catch (error) {
    log(`❌ Error: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}
