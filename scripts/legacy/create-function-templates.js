#!/usr/bin/env node

/**
 * Create function templates based on production function list
 * This creates a local development environment with all function stubs
 */

const fs = require('fs');
const path = require('path');

// Configuration
const FUNCTIONS_DIR = 'supabase/functions';
const BACKUP_DIR = `.supabase/backups/functions/${new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)}`;

// Production functions list (from API response)
const PRODUCTION_FUNCTIONS = [
  { slug: "get-user-by-id", id: "69d9b426-c4d1-4da8-95bf-82befdfcbfee", version: 228, verify_jwt: true },
  { slug: "upload-damage-photo", id: "c07c6a8f-cddc-4c95-afa5-f0479a216812", version: 209, verify_jwt: true },
  { slug: "upload-property-image", id: "450b8828-cd00-4688-b2db-9f65b39d181a", version: 206, verify_jwt: true },
  { slug: "fetch-ical-data", id: "0b93e9f6-936b-4029-aa36-dd91eaf2ef81", version: 228, verify_jwt: true },
  { slug: "sync-property-calendars", id: "c124bce9-428b-4782-ad20-f2a3cd873ff9", version: 210, verify_jwt: true },
  { slug: "upload-invoice-pdf", id: "12ca0354-164d-4d12-a368-1244cd1194e1", version: 197, verify_jwt: true },
  { slug: "scrape-product", id: "917a5237-1791-4e85-8305-f940381794bc", version: 181, verify_jwt: true },
  { slug: "send-email", id: "c2bc5086-b261-4627-8657-1a18726b2942", version: 130, verify_jwt: true },
  { slug: "ai-maintenance-items", id: "2e6af1c3-34d9-4368-bc08-6d35b77ad316", version: 148, verify_jwt: true },
  { slug: "ai-command-processor", id: "c1a46a04-ff8a-44b0-a7db-46727967cf91", version: 184, verify_jwt: true },
  { slug: "maintenance-response", id: "1ec8d53b-dcb2-47b7-8c1a-55b4c5975737", version: 107, verify_jwt: true },
  { slug: "admin-get-all-users", id: "dc381373-657e-45f4-8ee0-66507a40ce9b", version: 109, verify_jwt: true },
  { slug: "admin-get-users", id: "c1a2029c-6d62-4253-853c-a2a7ee8967c1", version: 101, verify_jwt: true },
  { slug: "admin-get-upload-url", id: "ba026713-3f12-438f-bedf-40ee2fd0b134", version: 98, verify_jwt: true },
  { slug: "admin-backup-database", id: "d267195b-140a-4623-bebc-e4aa0b379ad5", version: 93, verify_jwt: true },
  { slug: "admin-restore-database", id: "180ff3da-5917-40a6-8b8d-48d1cd663d90", version: 99, verify_jwt: true },
  { slug: "admin-create-user", id: "f1819e50-4ea6-45bb-9d93-af82d1a7ea97", version: 4, verify_jwt: true },
  { slug: "admin-delete-user", id: "2c4258ef-fc1b-49c9-9a9a-bec6834a8878", version: 8, verify_jwt: true },
  { slug: "admin-update-user", id: "2ad048f5-545f-402d-848e-9af010a86a4d", version: 4, verify_jwt: true },
  { slug: "verify-maintenance-token", id: "bbe26883-b14b-4505-9b0d-718e2235356b", version: 35, verify_jwt: true },
  { slug: "create-team-invitation", id: "82ba318a-7ea9-49bc-a0a7-c5c2d130a23d", version: 46, verify_jwt: true },
  { slug: "get-invitation-details", id: "20aea734-6d93-4304-8794-10aacac5afa9", version: 9, verify_jwt: true },
  { slug: "upload-damage-video", id: "6d74900b-4e6d-4a77-87d9-f428ec32c8a7", version: 8, verify_jwt: true },
  { slug: "generate-extension-token", id: "bd2b108c-0119-416d-9db7-dea64c1fb413", version: 24, verify_jwt: true },
  { slug: "upload-inventory-image", id: "d000b910-114b-48db-b56f-13f02ec23b4f", version: 10, verify_jwt: true },
  { slug: "register-service-provider", id: "d2ba9987-0205-466a-b553-f71243531f0c", version: 20, verify_jwt: true },
  { slug: "create-storage-buckets", id: "2a8911b5-7e8c-409f-8c71-cde662ff6db4", version: 12, verify_jwt: true },
  { slug: "manage-user", id: "c65c8dfe-5e40-465c-b3e2-268c3e20f5f4", version: 7, verify_jwt: true },
  { slug: "execute-sql", id: "819ad043-ae7f-4868-8dd2-6885c41d0088", version: 4, verify_jwt: true },
  { slug: "admin-force-delete-user", id: "695e4451-8906-4ffe-bbec-5c7d7c17fa58", version: 8, verify_jwt: true },
  { slug: "fix-missing-profiles", id: "71582506-19c6-41b1-9ab8-d4ac3b632a25", version: 7, verify_jwt: true },
  { slug: "get-maintenance-tasks", id: "f5f3e23d-47db-4d14-ae8b-1a724ee4ac86", version: 5, verify_jwt: false },
  { slug: "get-team-maintenance-tasks", id: "f64821e8-3144-4777-972b-f7240fb6d77e", version: 1, verify_jwt: false },
  { slug: "get-team-data", id: "7b4c5330-8551-4d7e-b246-a87499704ed3", version: 2, verify_jwt: false },
  { slug: "get-property-manager-teams", id: "cfb2a53c-4609-42e8-a2f2-d734c2851840", version: 1, verify_jwt: false },
  { slug: "accept-invitation", id: "423810e5-af16-4296-aec2-92e9911883b4", version: 2, verify_jwt: true },
  { slug: "accept-invitation-direct", id: "60c2e47e-690b-4404-b06c-96e125a89401", version: 23, verify_jwt: true },
  { slug: "process-recurring-tasks", id: "c11b02b5-3dd0-47a0-8257-f436762643f8", version: 1, verify_jwt: true }
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Create directory structure
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Create backup of existing functions
function backupExistingFunctions() {
  if (fs.existsSync(FUNCTIONS_DIR)) {
    log('📦 Creating backup of existing functions...', 'yellow');
    ensureDir(BACKUP_DIR);
    
    try {
      const { execSync } = require('child_process');
      execSync(`cp -r ${FUNCTIONS_DIR}/* ${BACKUP_DIR}/`, { stdio: 'ignore' });
      log(`✅ Backup created at: ${BACKUP_DIR}`, 'green');
    } catch (error) {
      log('⚠️  No existing functions to backup', 'yellow');
    }
  }
}

// Create a function template
function createFunctionTemplate(functionData) {
  const functionDir = path.join(FUNCTIONS_DIR, functionData.slug);
  ensureDir(functionDir);

  // Create deno.json
  const denoConfig = {
    imports: {
      "supabase": "https://esm.sh/@supabase/supabase-js@2"
    }
  };
  
  fs.writeFileSync(
    path.join(functionDir, 'deno.json'),
    JSON.stringify(denoConfig, null, 2)
  );

  // Create index.ts template
  const template = `// Function: ${functionData.slug}
// Status: Template - Replace with actual production code
// Created: ${new Date().toISOString()}
// Production Function ID: ${functionData.id}
// Version: ${functionData.version}
// JWT Verification: ${functionData.verify_jwt}

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'supabase'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client${functionData.verify_jwt ? `
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )` : `
    // This function doesn't require JWT verification
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )`}

    // TODO: Implement actual function logic here
    // This is a template - replace with the actual production code
    
    return new Response(
      JSON.stringify({ 
        message: "Function template created successfully",
        function: "${functionData.slug}",
        note: "Replace this template with actual production code",
        productionInfo: {
          id: "${functionData.id}",
          version: ${functionData.version},
          verifyJwt: ${functionData.verify_jwt}
        }
      }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200
      }
    )
  } catch (error) {
    console.error('Function error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500
      }
    )
  }
})
`;

  fs.writeFileSync(path.join(functionDir, 'index.ts'), template);
}

// Main function
function main() {
  try {
    log('🚀 Creating Supabase Function Templates', 'blue');
    log(`Functions Directory: ${FUNCTIONS_DIR}`, 'cyan');
    log(`Backup Directory: ${BACKUP_DIR}`, 'cyan');
    console.log();

    // Backup existing functions
    backupExistingFunctions();

    // Ensure functions directory exists
    ensureDir(FUNCTIONS_DIR);

    let created = 0;
    let skipped = 0;

    // Process each function
    for (const func of PRODUCTION_FUNCTIONS) {
      const functionDir = path.join(FUNCTIONS_DIR, func.slug);
      
      // Skip if function already exists and has actual code
      if (fs.existsSync(functionDir)) {
        const indexPath = path.join(functionDir, 'index.ts');
        if (fs.existsSync(indexPath)) {
          const content = fs.readFileSync(indexPath, 'utf8');
          if (!content.includes('Template - Replace with actual production code')) {
            log(`⏭️  Skipping existing function: ${func.slug}`, 'yellow');
            skipped++;
            continue;
          }
        }
      }

      try {
        log(`📝 Creating template for: ${func.slug}`, 'yellow');
        createFunctionTemplate(func);
        log(`✅ Created template for: ${func.slug}`, 'green');
        created++;
        
      } catch (error) {
        log(`❌ Failed to create template for: ${func.slug} - ${error.message}`, 'red');
      }
    }

    console.log();
    log('📊 Template Creation Summary', 'blue');
    log(`✅ Successfully created templates: ${created} functions`, 'green');
    log(`⏭️  Skipped existing functions: ${skipped} functions`, 'yellow');
    log(`📁 Backup location: ${BACKUP_DIR}`, 'cyan');

    console.log();
    log('🎉 Function template creation completed!', 'green');
    log('Next steps:', 'blue');
    console.log('   1. Review the function templates in:', FUNCTIONS_DIR);
    console.log('   2. Replace templates with actual production code');
    console.log('   3. Test functions locally with: supabase functions serve');
    console.log('   4. Deploy when ready with: supabase functions deploy <function-name>');

  } catch (error) {
    log(`❌ Error: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}
