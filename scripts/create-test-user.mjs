#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'http://127.0.0.1:54321'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function createTestUser() {
  console.log('Creating test user...')
  
  // Create user in auth.users
  const { data: authData, error: authError } = await supabase.auth.admin.createUser({
    email: '<EMAIL>',
    password: 'password123',
    email_confirm: true,
    user_metadata: {
      first_name: 'Test',
      last_name: 'User'
    }
  })

  if (authError) {
    console.error('Error creating auth user:', authError)
    return
  }

  console.log('Auth user created:', authData.user.id, authData.user.email)

  // Create profile
  const { data: profileData, error: profileError } = await supabase
    .from('profiles')
    .insert({
      id: authData.user.id,
      first_name: 'Test',
      last_name: 'User',
      email: '<EMAIL>',
      role: 'property_manager'
    })

  if (profileError) {
    console.error('Error creating profile:', profileError)
    return
  }

  console.log('Profile created successfully!')
  console.log('You can now login with:')
  console.log('Email: <EMAIL>')
  console.log('Password: password123')
}

createTestUser().catch(console.error)
