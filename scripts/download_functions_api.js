#!/usr/bin/env node

/**
 * Alternative approach to download Edge Functions using direct API calls
 * Since the CLI eszip bundling is failing, we'll use the Management API
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

const PROJECT_REF = 'pwaeknalhosfwuxkpaet';
const FUNCTIONS_DIR = './supabase/functions';

// You'll need to get a personal access token from https://supabase.com/dashboard/account/tokens
const ACCESS_TOKEN = process.env.SUPABASE_ACCESS_TOKEN || 'sbp_YOUR_TOKEN_HERE';

const functions = [
  'get-user-by-id',
  'upload-damage-photo',
  'upload-property-image',
  'fetch-ical-data',
  'sync-property-calendars',
  'upload-invoice-pdf',
  'scrape-product',
  'send-email',
  'ai-maintenance-items',
  'maintenance-response',
  'admin-get-all-users',
  'admin-get-users',
  'admin-get-upload-url',
  'admin-backup-database',
  'admin-restore-database',
  'admin-create-user',
  'admin-delete-user',
  'admin-update-user',
  'verify-maintenance-token',
  'create-team-invitation',
  'get-invitation-details',
  'upload-damage-video',
  'generate-extension-token',
  'upload-inventory-image',
  'register-service-provider',
  'create-storage-buckets',
  'manage-user',
  'execute-sql',
  'admin-force-delete-user',
  'fix-missing-profiles',
  'get-maintenance-tasks',
  'get-team-maintenance-tasks',
  'get-team-data',
  'get-property-manager-teams',
  'accept-invitation',
  'accept-invitation-direct',
  'process-recurring-tasks'
];

function makeRequest(url, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      headers: {
        'Authorization': `Bearer ${ACCESS_TOKEN}`,
        'Content-Type': 'application/json',
        ...headers
      }
    };

    https.get(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          try {
            resolve(JSON.parse(data));
          } catch (e) {
            resolve(data); // Return raw data if not JSON
          }
        } else {
          reject(new Error(`HTTP ${res.statusCode}: ${data}`));
        }
      });
    }).on('error', reject);
  });
}

async function downloadFunction(functionName) {
  try {
    console.log(`Downloading ${functionName}...`);
    
    // Get function metadata
    const functionInfo = await makeRequest(
      `https://api.supabase.com/v1/projects/${PROJECT_REF}/functions/${functionName}`
    );
    
    // Get function source code
    const functionBody = await makeRequest(
      `https://api.supabase.com/v1/projects/${PROJECT_REF}/functions/${functionName}/body`
    );
    
    // Create function directory
    const functionDir = path.join(FUNCTIONS_DIR, functionName);
    if (!fs.existsSync(functionDir)) {
      fs.mkdirSync(functionDir, { recursive: true });
    }
    
    // Write index.ts
    fs.writeFileSync(
      path.join(functionDir, 'index.ts'),
      functionBody
    );
    
    console.log(`✅ Downloaded ${functionName}`);
    
  } catch (error) {
    console.error(`❌ Failed to download ${functionName}:`, error.message);
  }
}

async function main() {
  if (ACCESS_TOKEN === 'sbp_YOUR_TOKEN_HERE') {
    console.error('❌ Please set SUPABASE_ACCESS_TOKEN environment variable');
    console.error('   Get your token from: https://supabase.com/dashboard/account/tokens');
    process.exit(1);
  }
  
  console.log(`Starting download of ${functions.length} functions...`);
  
  for (const functionName of functions) {
    await downloadFunction(functionName);
    // Small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('🎉 Download complete!');
}

if (require.main === module) {
  main().catch(console.error);
}
