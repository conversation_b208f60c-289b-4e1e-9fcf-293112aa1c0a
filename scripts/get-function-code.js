#!/usr/bin/env node

/**
 * Helper script to retrieve production function code
 * This script provides various methods to get the actual function implementations
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Function metadata from production
const FUNCTION_METADATA = {
  "get-user-by-id": { id: "69d9b426-c4d1-4da8-95bf-82befdfcbfee", version: 228, verify_jwt: true },
  "upload-damage-photo": { id: "c07c6a8f-cddc-4c95-afa5-f0479a216812", version: 209, verify_jwt: true },
  "upload-property-image": { id: "450b8828-cd00-4688-b2db-9f65b39d181a", version: 206, verify_jwt: true },
  "fetch-ical-data": { id: "0b93e9f6-936b-4029-aa36-dd91eaf2ef81", version: 228, verify_jwt: true },
  "sync-property-calendars": { id: "c124bce9-428b-4782-ad20-f2a3cd873ff9", version: 210, verify_jwt: true },
  "upload-invoice-pdf": { id: "12ca0354-164d-4d12-a368-1244cd1194e1", version: 197, verify_jwt: true },
  "scrape-product": { id: "917a5237-1791-4e85-8305-f940381794bc", version: 181, verify_jwt: true },
  "send-email": { id: "c2bc5086-b261-4627-8657-1a18726b2942", version: 130, verify_jwt: true },
  "ai-maintenance-items": { id: "2e6af1c3-34d9-4368-bc08-6d35b77ad316", version: 148, verify_jwt: true },
  "ai-command-processor": { id: "c1a46a04-ff8a-44b0-a7db-46727967cf91", version: 184, verify_jwt: true },
  "maintenance-response": { id: "1ec8d53b-dcb2-47b7-8c1a-55b4c5975737", version: 107, verify_jwt: true },
  "admin-get-all-users": { id: "dc381373-657e-45f4-8ee0-66507a40ce9b", version: 109, verify_jwt: true },
  "admin-get-users": { id: "c1a2029c-6d62-4253-853c-a2a7ee8967c1", version: 101, verify_jwt: true },
  "admin-get-upload-url": { id: "ba026713-3f12-438f-bedf-40ee2fd0b134", version: 98, verify_jwt: true },
  "admin-backup-database": { id: "d267195b-140a-4623-bebc-e4aa0b379ad5", version: 93, verify_jwt: true },
  "admin-restore-database": { id: "180ff3da-5917-40a6-8b8d-48d1cd663d90", version: 99, verify_jwt: true },
  "admin-create-user": { id: "f1819e50-4ea6-45bb-9d93-af82d1a7ea97", version: 4, verify_jwt: true },
  "admin-delete-user": { id: "2c4258ef-fc1b-49c9-9a9a-bec6834a8878", version: 8, verify_jwt: true },
  "admin-update-user": { id: "2ad048f5-545f-402d-848e-9af010a86a4d", version: 4, verify_jwt: true },
  "verify-maintenance-token": { id: "bbe26883-b14b-4505-9b0d-718e2235356b", version: 35, verify_jwt: true },
  "create-team-invitation": { id: "82ba318a-7ea9-49bc-a0a7-c5c2d130a23d", version: 46, verify_jwt: true },
  "get-invitation-details": { id: "20aea734-6d93-4304-8794-10aacac5afa9", version: 9, verify_jwt: true },
  "upload-damage-video": { id: "6d74900b-4e6d-4a77-87d9-f428ec32c8a7", version: 8, verify_jwt: true },
  "generate-extension-token": { id: "bd2b108c-0119-416d-9db7-dea64c1fb413", version: 24, verify_jwt: true },
  "upload-inventory-image": { id: "d000b910-114b-48db-b56f-13f02ec23b4f", version: 10, verify_jwt: true },
  "register-service-provider": { id: "d2ba9987-0205-466a-b553-f71243531f0c", version: 20, verify_jwt: true },
  "create-storage-buckets": { id: "2a8911b5-7e8c-409f-8c71-cde662ff6db4", version: 12, verify_jwt: true },
  "manage-user": { id: "c65c8dfe-5e40-465c-b3e2-268c3e20f5f4", version: 7, verify_jwt: true },
  "execute-sql": { id: "819ad043-ae7f-4868-8dd2-6885c41d0088", version: 4, verify_jwt: true },
  "admin-force-delete-user": { id: "695e4451-8906-4ffe-bbec-5c7d7c17fa58", version: 8, verify_jwt: true },
  "fix-missing-profiles": { id: "71582506-19c6-41b1-9ab8-d4ac3b632a25", version: 7, verify_jwt: true },
  "get-maintenance-tasks": { id: "f5f3e23d-47db-4d14-ae8b-1a724ee4ac86", version: 5, verify_jwt: false },
  "get-team-maintenance-tasks": { id: "f64821e8-3144-4777-972b-f7240fb6d77e", version: 1, verify_jwt: false },
  "get-team-data": { id: "7b4c5330-8551-4d7e-b246-a87499704ed3", version: 2, verify_jwt: false },
  "get-property-manager-teams": { id: "cfb2a53c-4609-42e8-a2f2-d734c2851840", version: 1, verify_jwt: false },
  "accept-invitation": { id: "423810e5-af16-4296-aec2-92e9911883b4", version: 2, verify_jwt: true },
  "accept-invitation-direct": { id: "60c2e47e-690b-4404-b06c-96e125a89401", version: 23, verify_jwt: true },
  "process-recurring-tasks": { id: "c11b02b5-3dd0-47a0-8257-f436762643f8", version: 1, verify_jwt: true }
};

function showHelp() {
  log('🔧 Supabase Function Code Retrieval Helper', 'blue');
  console.log();
  log('Usage:', 'yellow');
  console.log('  node scripts/get-function-code.js <command> [function-name]');
  console.log();
  log('Commands:', 'yellow');
  console.log('  list                    - List all functions and their status');
  console.log('  info <function-name>    - Show function metadata');
  console.log('  template <function-name> - Show template location');
  console.log('  methods                 - Show methods to get production code');
  console.log('  git-search <function>   - Search git history for function');
  console.log();
  log('Examples:', 'cyan');
  console.log('  node scripts/get-function-code.js list');
  console.log('  node scripts/get-function-code.js info upload-damage-photo');
  console.log('  node scripts/get-function-code.js git-search upload-damage-photo');
}

function listFunctions() {
  log('📋 Function Status Overview', 'blue');
  console.log();
  
  const functionsDir = 'supabase/functions';
  let templateCount = 0;
  let implementedCount = 0;
  
  for (const [funcName, metadata] of Object.entries(FUNCTION_METADATA)) {
    const functionPath = path.join(functionsDir, funcName, 'index.ts');
    let status = '❓ Unknown';
    
    if (fs.existsSync(functionPath)) {
      const content = fs.readFileSync(functionPath, 'utf8');
      if (content.includes('Template - Replace with actual production code')) {
        status = '📝 Template';
        templateCount++;
      } else {
        status = '✅ Implemented';
        implementedCount++;
      }
    } else {
      status = '❌ Missing';
    }
    
    console.log(`  ${status} ${funcName} (v${metadata.version})`);
  }
  
  console.log();
  log(`📊 Summary: ${implementedCount} implemented, ${templateCount} templates, ${Object.keys(FUNCTION_METADATA).length - implementedCount - templateCount} missing`, 'cyan');
}

function showFunctionInfo(functionName) {
  if (!FUNCTION_METADATA[functionName]) {
    log(`❌ Function '${functionName}' not found`, 'red');
    return;
  }
  
  const metadata = FUNCTION_METADATA[functionName];
  const functionPath = path.join('supabase/functions', functionName, 'index.ts');
  
  log(`📋 Function Information: ${functionName}`, 'blue');
  console.log();
  console.log(`  ID: ${metadata.id}`);
  console.log(`  Version: ${metadata.version}`);
  console.log(`  JWT Verification: ${metadata.verify_jwt}`);
  console.log(`  Local Path: ${functionPath}`);
  console.log(`  Exists Locally: ${fs.existsSync(functionPath) ? '✅ Yes' : '❌ No'}`);
  
  if (fs.existsSync(functionPath)) {
    const content = fs.readFileSync(functionPath, 'utf8');
    const isTemplate = content.includes('Template - Replace with actual production code');
    console.log(`  Status: ${isTemplate ? '📝 Template' : '✅ Implemented'}`);
  }
}

function showRetrievalMethods() {
  log('🔍 Methods to Get Production Function Code', 'blue');
  console.log();
  
  log('1. Git History Search', 'yellow');
  console.log('   Search your git repository for the original function implementations:');
  console.log('   git log --all --grep="function-name"');
  console.log('   git log --all --oneline | grep "function-name"');
  console.log();
  
  log('2. Supabase Dashboard', 'yellow');
  console.log('   Visit your Supabase project dashboard:');
  console.log('   https://supabase.com/dashboard/project/pwaeknalhosfwuxkpaet/functions');
  console.log();
  
  log('3. Team Collaboration', 'yellow');
  console.log('   Ask team members who have the original source code');
  console.log();
  
  log('4. API Documentation', 'yellow');
  console.log('   Recreate functions based on API documentation and business logic');
  console.log();
  
  log('5. Production Testing', 'yellow');
  console.log('   Test production functions to understand their behavior:');
  console.log('   curl -X POST "https://pwaeknalhosfwuxkpaet.supabase.co/functions/v1/function-name"');
}

function searchGitHistory(functionName) {
  log(`🔍 Searching Git History for: ${functionName}`, 'blue');
  console.log();
  
  const { exec } = require('child_process');
  
  // Search for commits mentioning the function
  exec(`git log --all --oneline --grep="${functionName}"`, (error, stdout, stderr) => {
    if (stdout.trim()) {
      log('📝 Commits mentioning this function:', 'green');
      console.log(stdout);
    } else {
      log('No commits found mentioning this function name', 'yellow');
    }
  });
  
  // Search for files containing the function name
  exec(`git log --all --oneline -S "${functionName}"`, (error, stdout, stderr) => {
    if (stdout.trim()) {
      log('📁 Commits with code changes mentioning this function:', 'green');
      console.log(stdout);
    }
  });
}

// Main execution
const args = process.argv.slice(2);
const command = args[0];
const functionName = args[1];

switch (command) {
  case 'list':
    listFunctions();
    break;
  case 'info':
    if (!functionName) {
      log('❌ Please provide a function name', 'red');
      process.exit(1);
    }
    showFunctionInfo(functionName);
    break;
  case 'template':
    if (!functionName) {
      log('❌ Please provide a function name', 'red');
      process.exit(1);
    }
    console.log(path.join('supabase/functions', functionName, 'index.ts'));
    break;
  case 'methods':
    showRetrievalMethods();
    break;
  case 'git-search':
    if (!functionName) {
      log('❌ Please provide a function name', 'red');
      process.exit(1);
    }
    searchGitHistory(functionName);
    break;
  default:
    showHelp();
}
