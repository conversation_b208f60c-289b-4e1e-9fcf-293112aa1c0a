#!/bin/bash

# Download all Edge Functions from remote Supabase project using legacy bundle
# Based on the function list we saw earlier

functions=(
    "get-user-by-id"
    "upload-damage-photo"
    "upload-property-image"
    "fetch-ical-data"
    "sync-property-calendars"
    "upload-invoice-pdf"
    "scrape-product"
    "send-email"
    "ai-maintenance-items"
    "ai-command-processor"
    "maintenance-response"
    "admin-get-all-users"
    "admin-get-users"
    "admin-get-upload-url"
    "admin-backup-database"
    "admin-restore-database"
    "admin-create-user"
    "admin-delete-user"
    "admin-update-user"
    "verify-maintenance-token"
    "create-team-invitation"
    "get-invitation-details"
    "upload-damage-video"
    "generate-extension-token"
    "upload-inventory-image"
    "register-service-provider"
    "create-storage-buckets"
    "manage-user"
    "execute-sql"
    "admin-force-delete-user"
    "fix-missing-profiles"
    "get-maintenance-tasks"
    "get-team-maintenance-tasks"
    "get-team-data"
    "get-property-manager-teams"
    "accept-invitation"
    "accept-invitation-direct"
    "process-recurring-tasks"
)

echo "Downloading ${#functions[@]} Edge Functions from remote project using legacy bundle..."

for func in "${functions[@]}"; do
    echo "Downloading function: $func"
    supabase functions download "$func" --project-ref pwaeknalhosfwuxkpaet --legacy-bundle
    if [ $? -eq 0 ]; then
        echo "✅ Successfully downloaded: $func"
    else
        echo "❌ Failed to download: $func"
    fi
    echo ""
done

echo "Download complete! All functions should now be available locally."
