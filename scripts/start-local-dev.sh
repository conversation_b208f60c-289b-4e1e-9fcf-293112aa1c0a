#!/bin/bash

# Start Local Supabase Development Environment
# This script starts the local Supabase instance and serves the Edge Functions

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Supabase Local Development Environment${NC}"
echo ""

# Check if supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo -e "${RED}❌ Supabase CLI is not installed. Please install it first.${NC}"
    echo "Visit: https://supabase.com/docs/guides/cli"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

echo -e "${YELLOW}📋 Checking Supabase status...${NC}"

# Check if Supabase is already running
if supabase status &> /dev/null; then
    echo -e "${GREEN}✅ Supabase is already running${NC}"
    supabase status
else
    echo -e "${YELLOW}🔄 Starting Supabase...${NC}"
    supabase start
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Supabase started successfully${NC}"
    else
        echo -e "${RED}❌ Failed to start Supabase${NC}"
        exit 1
    fi
fi

echo ""
echo -e "${BLUE}📊 Local Development Environment Info${NC}"
echo -e "${CYAN}API URL: http://127.0.0.1:54321${NC}"
echo -e "${CYAN}GraphQL URL: http://127.0.0.1:54321/graphql/v1${NC}"
echo -e "${CYAN}S3 Storage URL: http://127.0.0.1:54321/storage/v1/s3${NC}"
echo -e "${CYAN}DB URL: postgresql://postgres:postgres@127.0.0.1:54322/postgres${NC}"
echo -e "${CYAN}Studio URL: http://127.0.0.1:54323${NC}"
echo -e "${CYAN}Inbucket URL: http://127.0.0.1:54324${NC}"

echo ""
echo -e "${YELLOW}🔧 Available Functions:${NC}"
if [ -d "supabase/functions" ]; then
    for func_dir in supabase/functions/*/; do
        if [ -d "$func_dir" ]; then
            func_name=$(basename "$func_dir")
            echo -e "   • ${func_name}"
        fi
    done
else
    echo -e "${RED}   No functions directory found${NC}"
fi

echo ""
echo -e "${BLUE}🎯 Next Steps:${NC}"
echo "1. Open Supabase Studio: http://127.0.0.1:54323"
echo "2. Start function development server:"
echo -e "   ${YELLOW}supabase functions serve${NC}"
echo "3. Test functions at: http://127.0.0.1:54321/functions/v1/function-name"
echo "4. View function logs:"
echo -e "   ${YELLOW}supabase functions logs function-name${NC}"

echo ""
echo -e "${GREEN}🎉 Local development environment is ready!${NC}"

# Ask if user wants to start function server
echo ""
read -p "Do you want to start the functions server now? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}🔄 Starting functions server...${NC}"
    supabase functions serve
fi
