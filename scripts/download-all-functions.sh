#!/bin/bash

# Download all Supabase Edge Functions from production to local development environment
# This creates a sandboxed development environment without affecting production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_REF="pwaeknalhosfwuxkpaet"
FUNCTIONS_DIR="supabase/functions"
BACKUP_DIR=".supabase/backups/functions/$(date +%Y%m%d_%H%M%S)"

# Function list from production (extracted from API response)
FUNCTIONS=(
    "get-user-by-id"
    "upload-damage-photo"
    "upload-property-image"
    "fetch-ical-data"
    "sync-property-calendars"
    "upload-invoice-pdf"
    "scrape-product"
    "send-email"
    "ai-maintenance-items"
    "ai-command-processor"
    "maintenance-response"
    "admin-get-all-users"
    "admin-get-users"
    "admin-get-upload-url"
    "admin-backup-database"
    "admin-restore-database"
    "admin-create-user"
    "admin-delete-user"
    "admin-update-user"
    "verify-maintenance-token"
    "create-team-invitation"
    "get-invitation-details"
    "upload-damage-video"
    "generate-extension-token"
    "upload-inventory-image"
    "register-service-provider"
    "create-storage-buckets"
    "manage-user"
    "execute-sql"
    "admin-force-delete-user"
    "fix-missing-profiles"
    "get-maintenance-tasks"
    "get-team-maintenance-tasks"
    "get-team-data"
    "get-property-manager-teams"
    "accept-invitation"
    "accept-invitation-direct"
    "process-recurring-tasks"
)

echo -e "${BLUE}🚀 Starting Supabase Functions Download${NC}"
echo -e "${YELLOW}Project: ${PROJECT_REF}${NC}"
echo -e "${YELLOW}Functions Directory: ${FUNCTIONS_DIR}${NC}"
echo -e "${YELLOW}Backup Directory: ${BACKUP_DIR}${NC}"
echo ""

# Check if supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo -e "${RED}❌ Supabase CLI is not installed. Please install it first.${NC}"
    echo "Visit: https://supabase.com/docs/guides/cli"
    exit 1
fi

# Check if we're logged in
if ! supabase projects list &> /dev/null; then
    echo -e "${RED}❌ Not logged in to Supabase. Please run 'supabase login' first.${NC}"
    exit 1
fi

# Create backup directory
echo -e "${BLUE}📁 Creating backup directory...${NC}"
mkdir -p "$BACKUP_DIR"

# Backup existing functions if they exist
if [ -d "$FUNCTIONS_DIR" ]; then
    echo -e "${YELLOW}📦 Backing up existing functions...${NC}"
    cp -r "$FUNCTIONS_DIR"/* "$BACKUP_DIR/" 2>/dev/null || true
    echo -e "${GREEN}✅ Backup created at: $BACKUP_DIR${NC}"
fi

# Create functions directory if it doesn't exist
mkdir -p "$FUNCTIONS_DIR"

# Download each function
echo -e "${BLUE}⬇️  Downloading functions...${NC}"
DOWNLOADED=0
FAILED=0

for func in "${FUNCTIONS[@]}"; do
    echo -e "${YELLOW}Downloading: $func${NC}"
    
    # Create function directory
    mkdir -p "$FUNCTIONS_DIR/$func"
    
    # Try to download the function using supabase CLI
    if timeout 30 supabase functions download "$func" --project-ref "$PROJECT_REF" --output "$FUNCTIONS_DIR" 2>/dev/null; then
        echo -e "${GREEN}✅ Downloaded: $func${NC}"
        ((DOWNLOADED++))
    else
        echo -e "${YELLOW}⚠️  CLI download failed for: $func, trying alternative method...${NC}"

        # Alternative: Create basic function structure and note for manual retrieval
        mkdir -p "$FUNCTIONS_DIR/$func"

        # Create a basic deno.json for the function
        cat > "$FUNCTIONS_DIR/$func/deno.json" << EOF
{
  "imports": {
    "supabase": "https://esm.sh/@supabase/supabase-js@2"
  }
}
EOF

        # Create a placeholder index.ts file
        cat > "$FUNCTIONS_DIR/$func/index.ts" << EOF
// Function: $func
// Status: Placeholder - needs manual code retrieval from production
// TODO: Replace this with actual function code from production

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'supabase'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // TODO: Implement actual function logic here
    return new Response(
      JSON.stringify({
        error: "Function not implemented",
        message: "This function needs to be manually implemented. Check production for actual code.",
        function: "$func"
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 501
      }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500
      }
    )
  }
})
EOF

        echo -e "${YELLOW}📝 Created placeholder for: $func${NC}"
        ((FAILED++))
    fi
done

echo ""
echo -e "${BLUE}📊 Download Summary${NC}"
echo -e "${GREEN}✅ Successfully downloaded: $DOWNLOADED functions${NC}"
echo -e "${RED}❌ Failed to download: $FAILED functions${NC}"
echo -e "${YELLOW}📁 Backup location: $BACKUP_DIR${NC}"

if [ $FAILED -gt 0 ]; then
    echo ""
    echo -e "${YELLOW}⚠️  Some functions failed to download. This might be due to:${NC}"
    echo "   - Network connectivity issues"
    echo "   - Permission restrictions"
    echo "   - Functions that were deployed differently"
    echo ""
    echo -e "${BLUE}💡 You can manually retrieve these functions by:${NC}"
    echo "   1. Checking the Supabase dashboard"
    echo "   2. Using the Supabase API directly"
    echo "   3. Contacting your team members who have the source code"
fi

echo ""
echo -e "${GREEN}🎉 Function download process completed!${NC}"
echo -e "${BLUE}Next steps:${NC}"
echo "   1. Review the downloaded functions in: $FUNCTIONS_DIR"
echo "   2. Test the functions locally with: supabase functions serve"
echo "   3. Make your changes in the local environment"
echo "   4. Deploy when ready with: supabase functions deploy <function-name>"
