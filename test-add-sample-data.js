// Test script to add sample data to verify dashboard functionality
// Run this in the browser console after logging in

console.log('🏠 Adding Sample Data for Dashboard Testing');
console.log('==========================================');

// Import Supabase client (assuming it's available globally or via module)
const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

// Function to add sample data
async function addSampleData() {
    try {
        // Get current user
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) {
            console.error('❌ No authenticated user found:', userError);
            return;
        }
        
        console.log('✅ User found:', user.id);
        
        // 1. Add a sample property
        console.log('📍 Adding sample property...');
        const { data: property, error: propertyError } = await supabase
            .from('properties')
            .insert({
                name: 'Test Property',
                address: '123 Test Street',
                city: 'Test City',
                state: 'CA',
                zip: '12345',
                bedrooms: 2,
                bathrooms: 1,
                budget: 1000,
                user_id: user.id,
                timezone: 'America/Los_Angeles',
                check_in_time: '15:00',
                check_out_time: '11:00'
            })
            .select()
            .single();
            
        if (propertyError) {
            console.error('❌ Error adding property:', propertyError);
            return;
        }
        
        console.log('✅ Property added:', property.id);
        
        // 2. Add a sample maintenance task
        console.log('🔧 Adding sample maintenance task...');
        const { data: task, error: taskError } = await supabase
            .from('maintenance_tasks')
            .insert({
                title: 'Test Maintenance Task',
                description: 'This is a test maintenance task',
                status: 'new',
                severity: 'medium',
                due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
                property_id: property.id,
                user_id: user.id
            })
            .select()
            .single();
            
        if (taskError) {
            console.error('❌ Error adding maintenance task:', taskError);
        } else {
            console.log('✅ Maintenance task added:', task.id);
        }
        
        // 3. Add a sample inventory item
        console.log('📦 Adding sample inventory item...');
        const { data: item, error: itemError } = await supabase
            .from('inventory_items')
            .insert({
                name: 'Test Towels',
                category: 'linens',
                quantity: 5,
                min_quantity: 2,
                property_id: property.id,
                user_id: user.id
            })
            .select()
            .single();
            
        if (itemError) {
            console.error('❌ Error adding inventory item:', itemError);
        } else {
            console.log('✅ Inventory item added:', item.id);
        }
        
        // 4. Add a sample damage report
        console.log('🔨 Adding sample damage report...');
        const { data: damage, error: damageError } = await supabase
            .from('damage_reports')
            .insert({
                title: 'Test Damage Report',
                description: 'This is a test damage report',
                severity: 'medium',
                status: 'reported',
                property_id: property.id,
                user_id: user.id
            })
            .select()
            .single();
            
        if (damageError) {
            console.error('❌ Error adding damage report:', damageError);
        } else {
            console.log('✅ Damage report added:', damage.id);
        }
        
        console.log('');
        console.log('🎉 Sample data added successfully!');
        console.log('📊 Refresh the dashboard to see the data.');
        console.log('');
        console.log('Summary:');
        console.log(`- Property: ${property.name} (${property.id})`);
        console.log(`- Maintenance Task: ${task?.title || 'Failed'}`);
        console.log(`- Inventory Item: ${item?.name || 'Failed'}`);
        console.log(`- Damage Report: ${damage?.title || 'Failed'}`);
        
        // Trigger a dashboard refresh if the function exists
        if (window.refreshDashboardData) {
            console.log('🔄 Triggering dashboard refresh...');
            await window.refreshDashboardData();
        }
        
    } catch (error) {
        console.error('❌ Error adding sample data:', error);
    }
}

// Make the function available globally
window.addSampleData = addSampleData;

console.log('');
console.log('🛠️  Available Commands:');
console.log('- addSampleData() - Add sample data to test dashboard');
console.log('');
console.log('💡 Run addSampleData() to populate the dashboard with test data.');
