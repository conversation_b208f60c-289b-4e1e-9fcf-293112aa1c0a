<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Loading Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ccc;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Profile Loading Test</h1>
    <p>This page tests the profile loading functionality to debug the Settings page issue.</p>
    
    <div id="test-results"></div>
    
    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const supabaseUrl = 'http://127.0.0.1:54321';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';
        
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        const resultsDiv = document.getElementById('test-results');
        
        function addResult(title, content, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            resultsDiv.appendChild(div);
        }
        
        async function testProfileLoading() {
            try {
                // Test 1: Check current session
                addResult('Test 1: Checking current session...', 'Starting session check...');
                const { data: { session }, error: sessionError } = await supabase.auth.getSession();
                
                if (sessionError) {
                    addResult('Session Error', JSON.stringify(sessionError, null, 2), 'error');
                    return;
                }
                
                if (!session) {
                    addResult('No Session', 'No active session found. Please login first.', 'error');
                    return;
                }
                
                addResult('Session Found', `User ID: ${session.user.id}\nEmail: ${session.user.email}`, 'success');
                
                // Test 2: Fetch profile directly
                addResult('Test 2: Fetching profile directly...', 'Starting profile fetch...');
                const { data: profile, error: profileError } = await supabase
                    .from('profiles')
                    .select('*')
                    .eq('id', session.user.id)
                    .single();
                
                if (profileError) {
                    addResult('Profile Error', JSON.stringify(profileError, null, 2), 'error');
                } else if (profile) {
                    addResult('Profile Found', JSON.stringify(profile, null, 2), 'success');
                } else {
                    addResult('No Profile', 'Profile data is null', 'error');
                }
                
                // Test 3: Test multiple rapid fetches (simulate race condition)
                addResult('Test 3: Testing rapid fetches...', 'Starting rapid fetch test...');
                const promises = [];
                for (let i = 0; i < 5; i++) {
                    promises.push(
                        supabase
                            .from('profiles')
                            .select('*')
                            .eq('id', session.user.id)
                            .single()
                    );
                }
                
                const results = await Promise.all(promises);
                const successCount = results.filter(r => !r.error && r.data).length;
                addResult('Rapid Fetch Results', `${successCount}/5 successful fetches`, successCount === 5 ? 'success' : 'error');
                
                // Test 4: Test with delay (simulate timing issue)
                addResult('Test 4: Testing with delay...', 'Starting delayed fetch test...');
                await new Promise(resolve => setTimeout(resolve, 200));
                const { data: delayedProfile, error: delayedError } = await supabase
                    .from('profiles')
                    .select('*')
                    .eq('id', session.user.id)
                    .single();
                
                if (delayedError) {
                    addResult('Delayed Fetch Error', JSON.stringify(delayedError, null, 2), 'error');
                } else if (delayedProfile) {
                    addResult('Delayed Fetch Success', 'Profile fetched successfully after delay', 'success');
                } else {
                    addResult('Delayed Fetch Null', 'Profile data is null after delay', 'error');
                }
                
            } catch (error) {
                addResult('Test Error', JSON.stringify(error, null, 2), 'error');
            }
        }
        
        // Run tests when page loads
        testProfileLoading();
    </script>
</body>
</html>
