<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Context Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ccc;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
    </style>
</head>
<body>
    <h1>Auth Context Test</h1>
    <p>This page tests the auth context profile loading to verify the fix.</p>
    
    <div>
        <button class="btn-primary" onclick="testAuthFlow()">Test Auth Flow</button>
        <button class="btn-secondary" onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>
    
    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const supabaseUrl = 'http://127.0.0.1:54321';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';
        
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        const resultsDiv = document.getElementById('results');
        
        function addResult(title, content, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            resultsDiv.appendChild(div);
        }
        
        window.clearResults = function() {
            resultsDiv.innerHTML = '';
        };
        
        window.testAuthFlow = async function() {
            clearResults();
            
            try {
                // Simulate the auth context flow
                addResult('Step 1: Getting session...', 'Checking for existing session...');
                
                const { data: { session }, error: sessionError } = await supabase.auth.getSession();
                
                if (sessionError) {
                    addResult('Session Error', JSON.stringify(sessionError, null, 2), 'error');
                    return;
                }
                
                if (!session) {
                    addResult('No Session', 'No active session found. Please login first.', 'error');
                    return;
                }
                
                addResult('Session Found', `User ID: ${session.user.id}`, 'success');
                
                // Step 2: Immediately fetch profile (like our fix does)
                addResult('Step 2: Fetching profile immediately...', 'Simulating immediate profile fetch...');
                
                const startTime = Date.now();
                const { data: profile, error: profileError } = await supabase
                    .from('profiles')
                    .select('*')
                    .eq('id', session.user.id)
                    .single();
                
                const fetchTime = Date.now() - startTime;
                
                if (profileError) {
                    addResult('Profile Error', JSON.stringify(profileError, null, 2), 'error');
                } else if (profile) {
                    addResult('Profile Loaded', 
                        `✅ Profile loaded successfully in ${fetchTime}ms\n` +
                        `Name: ${profile.first_name} ${profile.last_name}\n` +
                        `Email: ${profile.email}\n` +
                        `Avatar: ${profile.avatar_url ? 'Yes' : 'No'}`, 
                        'success'
                    );
                } else {
                    addResult('Profile Null', 'Profile data is null', 'error');
                }
                
                // Step 3: Test the backup mechanism (with delay)
                addResult('Step 3: Testing backup mechanism...', 'Simulating backup fetch after delay...');
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const backupStartTime = Date.now();
                const { data: backupProfile, error: backupError } = await supabase
                    .from('profiles')
                    .select('*')
                    .eq('id', session.user.id)
                    .single();
                
                const backupFetchTime = Date.now() - backupStartTime;
                
                if (backupError) {
                    addResult('Backup Fetch Error', JSON.stringify(backupError, null, 2), 'error');
                } else if (backupProfile) {
                    addResult('Backup Fetch Success', 
                        `✅ Backup fetch successful in ${backupFetchTime}ms\n` +
                        `Profile data is consistent: ${JSON.stringify(profile) === JSON.stringify(backupProfile)}`, 
                        'success'
                    );
                } else {
                    addResult('Backup Fetch Null', 'Backup profile data is null', 'error');
                }
                
                addResult('Test Complete', 
                    `✅ Auth flow test completed successfully!\n` +
                    `The profile loading fix should work correctly.`, 
                    'success'
                );
                
            } catch (error) {
                addResult('Test Error', JSON.stringify(error, null, 2), 'error');
            }
        };
    </script>
</body>
</html>
