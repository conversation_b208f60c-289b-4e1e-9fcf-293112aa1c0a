// Secure Supabase client configuration using environment variables
import { createClient } from '@supabase/supabase-js';

// Environment detection and configuration
const SUPABASE_URL = import.meta.env?.VITE_SUPABASE_URL || 'https://pwaeknalhosfwuxkpaet.supabase.co';
const SUPABASE_PUBLISHABLE_KEY = import.meta.env?.VITE_SUPABASE_PUBLISHABLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4';

// Determine environment
const isLocal = SUPABASE_URL.includes('127.0.0.1') || SUPABASE_URL.includes('localhost');
const isProduction = SUPABASE_URL.includes('supabase.co');
const environment = isLocal ? 'local' : isProduction ? 'production' : 'unknown';

// Log configuration for debugging
if (typeof window !== 'undefined') {
  console.log('🔧 Supabase Configuration:');
  console.log('Vite Mode:', import.meta.env?.MODE || 'default');
  console.log('Environment:', environment);
  console.log('DEV mode:', import.meta.env?.DEV);
  console.log('URL:', SUPABASE_URL);
  console.log('Key prefix:', SUPABASE_PUBLISHABLE_KEY.substring(0, 20) + '...');
  console.log('Environment variables:');
  console.log('- VITE_SUPABASE_URL:', import.meta.env?.VITE_SUPABASE_URL ? 'Set' : 'Using default');
  console.log('- VITE_SUPABASE_PUBLISHABLE_KEY:', import.meta.env?.VITE_SUPABASE_PUBLISHABLE_KEY ? 'Set' : 'Using default');

  if (!import.meta.env?.VITE_SUPABASE_URL) {
    console.warn('⚠️ VITE_SUPABASE_URL not set, using production default');
  }
  if (!import.meta.env?.VITE_SUPABASE_PUBLISHABLE_KEY) {
    console.warn('⚠️ VITE_SUPABASE_PUBLISHABLE_KEY not set, using production default');
  }
}

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

// Create the Supabase client with standard configuration - no custom fetch, no complex logic
export const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  }
});
