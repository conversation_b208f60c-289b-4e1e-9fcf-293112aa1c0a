import React from 'react';
import { useAuth } from '@/contexts/AuthContext';

const AuthStateDebug: React.FC = () => {
  const { authState } = useAuth();

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: '#f0f0f0',
      border: '1px solid #ccc',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      fontFamily: 'monospace',
      zIndex: 9999,
      maxWidth: '300px',
      wordBreak: 'break-all'
    }}>
      <h4 style={{ margin: '0 0 10px 0', fontSize: '14px' }}>Auth State Debug</h4>
      <div><strong>User ID:</strong> {authState.user?.id || 'null'}</div>
      <div><strong>Email:</strong> {authState.user?.email || 'null'}</div>
      <div><strong>Is Authenticated:</strong> {authState.isAuthenticated ? 'true' : 'false'}</div>
      <div><strong>Is Loading:</strong> {authState.isLoading ? 'true' : 'false'}</div>
      <div><strong>Has Profile:</strong> {authState.profile ? 'true' : 'false'}</div>
      <div><strong>Profile Name:</strong> {authState.profile ? `${authState.profile.first_name} ${authState.profile.last_name}` : 'null'}</div>
      <div><strong>Session:</strong> {authState.session ? 'exists' : 'null'}</div>
    </div>
  );
};

export default AuthStateDebug;
