import React, { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { AuthState, UserProfile } from '@/types/auth';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { Camera, Loader2, Upload, X } from 'lucide-react';
import { uploadImage } from '@/utils/imageProcessing';

interface ProfileSettingsProps {
  profile: AuthState['profile'];
}

const ProfileSettings = ({ profile }: ProfileSettingsProps) => {
  const { refreshProfile } = useAuth();
  const [uploading, setUploading] = useState(false);
  const [firstName, setFirstName] = useState(profile?.first_name || '');
  const [lastName, setLastName] = useState(profile?.last_name || '');
  const [saving, setSaving] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState(profile?.avatar_url || '');

  // Debug logging
  console.log('[ProfileSettings] Rendered with profile:', profile);
  console.log('[ProfileSettings] Avatar URL:', avatarUrl);

  // Update local state when profile prop changes
  React.useEffect(() => {
    if (profile) {
      console.log('[ProfileSettings] Profile changed, updating local state:', profile);
      setFirstName(profile.first_name || '');
      setLastName(profile.last_name || '');
      setAvatarUrl(profile.avatar_url || '');
    }
  }, [profile]);

  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      setUploading(true);

      if (!event.target.files || event.target.files.length === 0) {
        return;
      }

      // Get the current user ID from auth
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      const file = event.target.files[0];
      
      // Use our new upload system
      const result = await uploadImage(file, 'avatar-images', `avatars/${user.id}`);

      // Update the profile with the new avatar URL
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ avatar_url: result.url })
        .eq('id', user.id);

      if (updateError) {
        throw updateError;
      }

      // Update local state
      setAvatarUrl(result.url);

      // Refresh the profile in the auth context
      await refreshProfile();

      toast.success('Avatar updated successfully');
    } catch (error) {
      console.error('Error uploading avatar:', error);
      toast.error(`Failed to upload avatar: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setUploading(false);
    }
  };

  const handleRemoveAvatar = async () => {
    try {
      setUploading(true);

      // Get the current user ID from auth
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      // Update the profile to remove the avatar URL
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ avatar_url: null })
        .eq('id', user.id);

      if (updateError) {
        throw updateError;
      }

      // Update local state
      setAvatarUrl('');

      // Refresh the profile in the auth context
      await refreshProfile();

      toast.success('Avatar removed successfully');
    } catch (error) {
      console.error('Error removing avatar:', error);
      toast.error('Failed to remove avatar');
    } finally {
      setUploading(false);
    }
  };

  const handleSaveProfile = async () => {
    try {
      setSaving(true);

      // Get the current user ID from auth
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      // Update the profile with the new name
      const { error } = await supabase
        .from('profiles')
        .update({
          first_name: firstName,
          last_name: lastName,
        })
        .eq('id', user.id);

      if (error) {
        throw error;
      }

      // Refresh the profile in the auth context
      await refreshProfile();

      toast.success('Profile updated successfully');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Profile Picture</h3>
        <div className="flex items-center gap-6">
          <div className="relative">
            <Avatar className="h-24 w-24 border-2 border-border">
              <AvatarImage src={avatarUrl} alt={profile?.first_name || 'User'} />
              <AvatarFallback className="text-2xl">
                {profile?.first_name?.charAt(0) || 'U'}
              </AvatarFallback>
            </Avatar>

            {uploading && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-full">
                <Loader2 className="h-8 w-8 text-white animate-spin" />
              </div>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
                disabled={uploading}
                asChild
              >
                <label>
                  <Camera className="h-4 w-4" />
                  Upload Photo
                  <input
                    type="file"
                    className="sr-only"
                    accept="image/*,.heic,.heif"
                    onChange={handleAvatarUpload}
                    disabled={uploading}
                  />
                </label>
              </Button>

              {avatarUrl && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2 text-destructive hover:text-destructive"
                  onClick={handleRemoveAvatar}
                  disabled={uploading}
                >
                  <X className="h-4 w-4" />
                  Remove
                </Button>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Recommended: Square JPG or PNG, max 1MB
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Personal Information</h3>
        <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              placeholder="Enter your first name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              placeholder="Enter your last name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              value={profile?.email || ''}
              disabled
              className="bg-muted"
            />
            <p className="text-xs text-muted-foreground">
              Email cannot be changed
            </p>
          </div>
          <div className="space-y-2">
            <Label htmlFor="role">Account Type</Label>
            <Input
              id="role"
              value={
                profile?.role === 'property_manager' ? 'Property Manager' :
                profile?.role === 'super_admin' ? 'Super Admin' :
                profile?.role === 'admin' ? 'Admin' :
                profile?.role === 'service_provider' ? 'Service Provider' :
                profile?.role === 'staff' ? 'Staff' :
                profile?.role || 'User'
              }
              disabled
              className="bg-muted"
            />
          </div>
        </div>

        <div className="flex justify-end mt-4">
          <Button
            onClick={handleSaveProfile}
            disabled={saving || uploading}
            className="flex items-center gap-2"
          >
            {saving && <Loader2 className="h-4 w-4 animate-spin" />}
            Save Profile
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProfileSettings;
