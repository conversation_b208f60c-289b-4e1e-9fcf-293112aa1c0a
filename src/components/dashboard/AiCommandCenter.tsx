
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Loader2, Send, Sparkles, AlertCircle, Info, Mic, RotateCcw, MessageCircle, Minimize2, Maximize2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import { showAIConfirmationToast, type AICommandResult } from '@/lib/utils';
import { useAiConversation } from '@/contexts/AiConversationContext';
import { createSpeechRecognition, isSpeechRecognitionSupported, getBrowserSpeechSupport, type SpeechRecognitionPolyfill } from '@/utils/speechRecognitionPolyfill';
import { getErrorSuggestions, suggestSimilarCommands, getCommandExamples } from '@/utils/aiErrorSuggestions';
import { processCommandLocally } from '@/utils/aiLocalAssistant';
import { cn } from '@/lib/utils';

// Use the AICommandResult interface from utils
type CommandResult = AICommandResult;

const AiCommandCenter: React.FC = () => {
  const [command, setCommand] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [showHistory, setShowHistory] = useState<boolean>(false);
  const [showSuggestions, setShowSuggestions] = useState<boolean>(false);
  const [isFloating, setIsFloating] = useState<boolean>(false);
  const [isMinimized, setIsMinimized] = useState<boolean>(false);
  const recognitionRef = useRef<SpeechRecognitionPolyfill | null>(null);
  const assistantRef = useRef<HTMLDivElement>(null);
  const [result, setResult] = useState<CommandResult | null>(null);
  const { authState } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { addMessage, getConversationContext, clearConversation, state } = useAiConversation();
  const userId = authState.user?.id;

  // Load minimized state from localStorage
  useEffect(() => {
    try {
      const saved = localStorage.getItem('ai-assistant-preferences');
      if (saved) {
        const prefs = JSON.parse(saved);
        setIsMinimized(prefs.isMinimized ?? false);
      }
    } catch (error) {
      console.warn('Failed to load AI assistant preferences:', error);
    }
  }, []);

  // Save minimized state to localStorage
  useEffect(() => {
    try {
      const prefs = { isMinimized };
      localStorage.setItem('ai-assistant-preferences', JSON.stringify(prefs));
    } catch (error) {
      console.warn('Failed to save AI assistant preferences:', error);
    }
  }, [isMinimized]);

  // Handle scroll to make the assistant float
  useEffect(() => {
    const handleScroll = () => {
      if (assistantRef.current) {
        const scrollY = window.scrollY;
        const shouldFloat = scrollY > 100; // Float after scrolling 100px
        setIsFloating(shouldFloat);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleCommandChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCommand(e.target.value);
    // Clear previous results when input changes
    if (result) setResult(null);
  };

  const handleAudioInput = () => {
    const speechSupport = getBrowserSpeechSupport();
    
    if (!recognitionRef.current) {
      recognitionRef.current = createSpeechRecognition();
      
      if (!recognitionRef.current) {
        toast.error("Speech Recognition is not available on this device.");
        return;
      }
      
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = false;
      recognitionRef.current.lang = 'en-US';

      recognitionRef.current.onstart = () => {
        setIsRecording(true);
        if (speechSupport.supported) {
          toast.info("Listening...");
        } else {
          toast.info("Speech input via text prompt...");
        }
      };

      recognitionRef.current.onresult = (event: SpeechRecognitionEvent) => {
        const transcript = event.results[0][0].transcript;
        setCommand(transcript);
        setIsRecording(false);
        toast.success("Voice input captured successfully.");
      };

      recognitionRef.current.onerror = (event: SpeechRecognitionErrorEvent) => {
        setIsRecording(false);
        console.error("Speech recognition error:", event.error);
        
        if (event.error === 'no-speech') {
          toast.error("No speech detected. Please try again.");
        } else if (event.error === 'network') {
          toast.error("Network error. Please check your connection.");
        } else {
          toast.error(`Voice input error: ${event.error}`);
        }
      };

      recognitionRef.current.onend = () => {
        setIsRecording(false);
      };
    }

    if (isRecording) {
      recognitionRef.current.stop();
    } else {
      recognitionRef.current.start();
    }
  };

  // Handle built-in commands locally
  const handleBuiltInCommands = (command: string): CommandResult | null => {
    const lowerCommand = command.toLowerCase().trim();
    
    // Help commands
    if (lowerCommand === 'help' || lowerCommand === 'help me' || lowerCommand === 'help me use this' || lowerCommand.includes('how to use')) {
      return {
        success: true,
        message: "🤖 AI Assistant Help - I can help you manage your properties! Here are some examples:",
        builtIn: true,
        showExamples: true
      };
    }
    
    // Show examples command
    if (lowerCommand.includes('show examples') || lowerCommand.includes('give me examples') || lowerCommand === 'examples') {
      return {
        success: true,
        message: "Here are some command examples you can try:",
        builtIn: true,
        showExamples: true
      };
    }
    
    // What can you do
    if (lowerCommand.includes('what can you do') || lowerCommand.includes('what do you do') || lowerCommand.includes('capabilities')) {
      return {
        success: true,
        message: "I can help you with property management tasks:",
        builtIn: true,
        capabilities: [
          "🏠 Add new properties with details",
          "🔧 Create maintenance tasks",
          "📋 Create damage reports",
          "📦 Manage inventory and stock levels", 
          "🛒 Generate purchase orders",
          "📋 Create collections and budgets",
          "💬 Remember our conversation context"
        ]
      };
    }
    
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!command.trim()) return;
    if (!userId) {
      toast.error("You must be logged in to use the AI assistant");
      return;
    }

    setIsLoading(true);
    setResult(null);

    // Check for built-in commands first
    const builtInResult = handleBuiltInCommands(command);
    if (builtInResult) {
      setResult(builtInResult);
      setIsLoading(false);
      
      // Add to conversation history
      addMessage({
        type: 'user',
        content: command,
        timestamp: new Date()
      });
      
      addMessage({
        type: 'ai',
        content: builtInResult.message,
        timestamp: new Date(),
        result: builtInResult
      });
      
      // Show suggestions panel for help commands
      if (builtInResult.showExamples) {
        setShowSuggestions(true);
      }
      
      return;
    }

    // Process command locally for validation and helpful feedback
    const localProcessing = processCommandLocally(command);
    if (!localProcessing.shouldProceed) {
      setResult(localProcessing.response);
      setIsLoading(false);
      
      // Add to conversation history
      addMessage({
        type: 'user',
        content: command,
        timestamp: new Date()
      });
      
      addMessage({
        type: 'ai',
        content: localProcessing.response.message,
        timestamp: new Date(),
        result: localProcessing.response
      });
      
      return;
    }

    try {
      console.log("Sending command to AI:", command);

      // Add user message to conversation history
      addMessage({
        type: 'user',
        content: command,
        timestamp: new Date()
      });

      // Get conversation context for better AI understanding
      const conversationContext = getConversationContext();
      const enhancedCommand = conversationContext ? `${conversationContext} ${command}` : command;

      // Call Supabase edge function with proper authentication and context
      const { data, error } = await supabase.functions.invoke('ai-command-processor', {
        body: {
          command: enhancedCommand,
          userId,
          originalCommand: command,
          hasContext: conversationContext.length > 0
        }
      });

      if (error) {
        throw new Error(error.message || 'Failed to process command');
      }

      console.log("Response from AI command processor:", data);

      // Add additional check for empty data
      if (!data) {
        throw new Error('No response received from AI command processor');
      }

      setResult(data);

      // Add AI response to conversation history
      addMessage({
        type: 'ai',
        content: data.message || 'Command processed successfully',
        timestamp: new Date(),
        result: data
      });

      // Use the enhanced toast with navigation support
      // Wrap in setTimeout to avoid render cycle issues
      setTimeout(() => {
        showAIConfirmationToast(data, {
          entityType: data.entityType || '',
          entityId: data.entityId,
          navigate
        });
      }, 0);

      // Still trigger refresh events for maintenance tasks to update the dashboard
      if (data.success && data.entityType === 'maintenance_task') {
        console.log('[AiCommandCenter] Maintenance task created, triggering refresh');
        setTimeout(() => {
          // Dispatch a custom event to refresh maintenance tasks
          window.dispatchEvent(new CustomEvent('force-refresh-maintenance'));

          // Also force a URL update to trigger a re-render
          const timestamp = Math.floor(Date.now() / 1000);
          window.history.replaceState(
            {},
            document.title,
            `${window.location.pathname}?refresh=${timestamp}`
          );
        }, 500);
      }

    } catch (error) {
      console.error("Error processing AI command:", error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      
      // Always get error suggestions for consistent error handling
      const errorSuggestion = getErrorSuggestions(errorMessage, command);
      
      // Check if the error response already has intelligent suggestions
      let errorResult: CommandResult;

      if (error.message && typeof error.message === 'object' && error.message.suggestions) {
        // Use intelligent suggestions from the AI processor
        errorResult = {
          success: false,
          message: error.message.message || errorMessage,
          suggestions: error.message.suggestions || [],
          category: error.message.category || 'general',
          intent: error.message.intent
        };
      } else {
        // Fallback to enhanced error suggestions
        let enhancedMessage = errorMessage;
        if (errorMessage.includes("couldn't understand") || errorMessage.includes("try again")) {
          enhancedMessage = `I need more details to help you. Here are some suggestions:`;
        }

        errorResult = {
          success: false,
          message: enhancedMessage,
          suggestions: errorSuggestion.suggestions,
          category: errorSuggestion.category
        };
      }
      setResult(errorResult);
      
      // Add error to conversation history
      addMessage({
        type: 'ai',
        content: `${errorSuggestion.message}: ${errorMessage}`,
        timestamp: new Date(),
        result: errorResult
      });
      
      // Show enhanced error toast with suggestions
      setTimeout(() => {
        toast.error(errorSuggestion.message, {
          description: errorSuggestion.suggestions[0],
          duration: 8000,
          action: {
            label: "Show Tips",
            onClick: () => setShowSuggestions(true)
          }
        });
      }, 0);
    } finally {
      setIsLoading(false);
    }
  };

  const getPlaceholderText = () => {
    const currentPath = location.pathname;

    // Context-aware suggestions based on current page
    let suggestions: string[] = [];

    if (currentPath.includes('/properties')) {
      suggestions = [
        "Add a property named Ocean View at 123 Beach Road, Miami, FL with 3 bedrooms",
        "Create a damage report at Beach House for a wine stain on the living room carpet",
        "Update the Beach House property to have 4 bedrooms instead of 3"
      ];
    } else if (currentPath.includes('/maintenance')) {
      suggestions = [
        "Create a maintenance task to fix the broken sink at Beach House",
        "Assign the HVAC repair task to John Smith",
        "Mark the plumbing task as completed at Ocean View"
      ];
    } else if (currentPath.includes('/inventory')) {
      suggestions = [
        "We're down to only 2 bath towels, we need a minimum of 12",
        "Add 10 wine glasses to the Beach House kitchen collection",
        "Create a purchase order for all low stock items"
      ];
    } else if (currentPath.includes('/damages')) {
      suggestions = [
        "Create a damage report at Beach House for a wine stain on the living room carpet",
        "Update the carpet damage status to resolved",
        "Generate an invoice for the Beach House carpet repair"
      ];
    } else if (currentPath.includes('/purchase-orders')) {
      suggestions = [
        "Create a purchase order for all low stock items",
        "Add cleaning supplies to the pending purchase order",
        "Mark the towel order as received"
      ];
    } else {
      // Default dashboard suggestions
      suggestions = [
        "Add a property named Ocean View at 123 Beach Road, Miami, FL with 3 bedrooms",
        "Create a damage report at Beach House for a wine stain on the living room carpet",
        "Create a maintenance task to fix the broken sink at Beach House",
        "Create a purchase order for all low stock items",
        "We're down to only 2 bath towels, we need a minimum of 12"
      ];
    }

    return suggestions[Math.floor(Math.random() * suggestions.length)];
  };

  return (
    <div
      ref={assistantRef}
      className={cn(
        "w-full bg-gradient-to-r from-blue-50/80 to-purple-50/80 dark:from-blue-950/50 dark:to-purple-950/50 border border-border/50 backdrop-blur-sm transition-all duration-300 rounded-xl mb-2 sm:mb-4",
        isFloating && "fixed top-2 sm:top-4 left-2 sm:left-4 right-2 sm:right-4 z-50 shadow-lg",
        isMinimized && "h-10 sm:h-12 overflow-hidden"
      )}
      style={{
        marginLeft: isFloating ? '0' : 'auto',
        marginRight: isFloating ? '0' : 'auto',
        maxWidth: isFloating ? 'calc(100vw - 2rem)' : '100%'
      }}
    >
      <div className="p-2 sm:p-3">
        {/* Header */}
        <div className="flex items-center justify-between mb-1 sm:mb-2">
          <div className="flex items-center gap-1.5 sm:gap-2">
            <Sparkles className="h-4 sm:h-5 w-4 sm:w-5 text-blue-600 dark:text-blue-400" />
            <h2 className="text-xs sm:text-sm font-semibold text-foreground">AI Assistant</h2>
            {isLoading && (
              <div className="animate-spin h-3 sm:h-4 w-3 sm:w-4 border-2 border-blue-500 border-t-transparent rounded-full" />
            )}
          </div>

          <div className="flex items-center gap-1">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowHistory(!showHistory)}
              className="h-8 w-8 p-0"
              title="Toggle conversation history"
            >
              <MessageCircle className="h-4 w-4" />
            </Button>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setIsMinimized(!isMinimized)}
              className="h-8 w-8 p-0"
              title={isMinimized ? "Expand" : "Minimize"}
            >
              {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {!isMinimized && (
          <>
            {/* Command Input */}
            <form onSubmit={handleSubmit} className="flex gap-1.5 sm:gap-2 mb-2 sm:mb-3">
              <div className="flex-1 relative">
                <Input
                  placeholder={`Try: "${getPlaceholderText()}"`}
                  value={command}
                  onChange={handleCommandChange}
                  className="pr-12 bg-white/80 dark:bg-gray-900/80 border-border/50"
                  disabled={isLoading}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleAudioInput}
                  className={cn(
                    "absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0",
                    isRecording && "text-red-500 animate-pulse"
                  )}
                  title={isRecording ? "Stop listening" : "Start voice input"}
                >
                  {isRecording ? <Loader2 className="h-4 w-4 animate-spin" /> : <Mic className="h-4 w-4" />}
                </Button>
              </div>
              <Button
                type="submit"
                disabled={!command.trim() || isLoading}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
              </Button>
            </form>

            {/* Result Display */}
            {result && (
        <div className={`mt-1 p-1.5 rounded text-xs ${
          result.success ? 'bg-green-50 dark:bg-green-950/50 text-green-700 dark:text-green-300' : 'bg-red-50 dark:bg-red-950/50 text-red-700 dark:text-red-300'
        }`}>
          <div className="flex items-center gap-1">
            {result.success ? (
              <Sparkles className="h-3 w-3 flex-shrink-0" />
            ) : (
              <AlertCircle className="h-3 w-3 flex-shrink-0" />
            )}
            <p className="text-xs leading-tight">{result.message}</p>
          </div>
          {!result.success && (result as any).missingInfo && (
            <div className="mt-1 pt-1 border-t border-current/20">
              <p className="text-xs font-medium mb-1">Missing information:</p>
              <ul className="text-xs opacity-90 list-disc list-inside">
                {(result as any).missingInfo.map((info: string, index: number) => (
                  <li key={index}>{info}</li>
                ))}
              </ul>
              {(result as any).suggestions && (result as any).suggestions.length > 0 && (
                <div className="mt-1">
                  <p className="text-xs font-medium">Try this format:</p>
                  <p className="text-xs opacity-90">"{(result as any).suggestions[0]}"</p>
                </div>
              )}
            </div>
          )}
          {!result.success && (result as any).suggestions && !(result as any).missingInfo && (
            <div className="mt-1 pt-1 border-t border-current/20">
              <p className="text-xs font-medium mb-1">Try this instead:</p>
              <p className="text-xs opacity-90">"{(result as any).suggestions[0]}"</p>
              {(result as any).suggestions.length > 1 && (
                <button
                  onClick={() => setShowSuggestions(true)}
                  className="text-xs underline mt-1 hover:no-underline"
                >
                  Show {(result as any).suggestions.length - 1} more suggestions
                </button>
              )}
            </div>
          )}
          {result.success && (result as any).capabilities && (
            <div className="mt-1 pt-1 border-t border-current/20">
              <div className="space-y-1">
                {(result as any).capabilities.map((capability: string, index: number) => (
                  <p key={index} className="text-xs opacity-90">{capability}</p>
                ))}
              </div>
              <button
                onClick={() => setShowSuggestions(true)}
                className="text-xs underline mt-1 hover:no-underline"
              >
                Show command examples
              </button>
            </div>
          )}
        </div>
      )}

      {showHistory && state.messages.length > 0 && (
        <div className="mt-2 p-2 bg-muted/20 rounded-md border max-h-48 overflow-y-auto">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-xs font-medium text-muted-foreground">Conversation History</h4>
            <span className="text-xs text-muted-foreground">{state.messages.length} messages</span>
          </div>
          <div className="space-y-1">
            {state.messages.slice(-10).map((msg) => (
              <div key={msg.id} className={`text-xs p-1.5 rounded ${
                msg.type === 'user' 
                  ? 'bg-primary/10 text-primary-foreground ml-4' 
                  : 'bg-secondary/50 text-secondary-foreground mr-4'
              }`}>
                <div className="flex items-center gap-1 mb-0.5">
                  <span className="font-medium capitalize">{msg.type}:</span>
                  <span className="text-xs opacity-70">
                    {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </span>
                </div>
                <p className="leading-tight">{msg.content}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {showSuggestions && (
        <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-950/20 rounded-md border border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-xs font-medium text-blue-700 dark:text-blue-300">Command Suggestions</h4>
            <button
              onClick={() => setShowSuggestions(false)}
              className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
            >
              Close
            </button>
          </div>
          <div className="space-y-1">
            {getCommandExamples(location.pathname).map((example, index) => (
              <button
                key={index}
                onClick={() => {
                  setCommand(example);
                  setShowSuggestions(false);
                }}
                className="block w-full text-left text-xs p-1.5 bg-white dark:bg-blue-900/30 rounded border hover:bg-blue-50 dark:hover:bg-blue-900/50 transition-colors"
              >
                {example}
              </button>
            ))}
          </div>
          {command && (
            <div className="mt-2 pt-2 border-t border-blue-200 dark:border-blue-800">
              <p className="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">Similar to your command:</p>
              <div className="space-y-1">
                {suggestSimilarCommands(command).map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setCommand(suggestion);
                      setShowSuggestions(false);
                    }}
                    className="block w-full text-left text-xs p-1.5 bg-blue-100 dark:bg-blue-900/40 rounded hover:bg-blue-200 dark:hover:bg-blue-900/60 transition-colors"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          )}
            </div>
          )}
          </>
        )}
      </div>
    </div>
  );
};

export default AiCommandCenter;
