
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ShieldAlert, RefreshCw } from 'lucide-react';
import ErrorState from '@/components/ui/ErrorState';

interface AdminErrorStateProps {
  error: string;
  isRefreshing: boolean;
  onRefresh: () => Promise<void>;
}

const AdminErrorState: React.FC<AdminErrorStateProps> = ({
  error,
  isRefreshing,
  onRefresh
}) => {
  const navigate = useNavigate();

  const actions = (
    <div className="flex flex-col sm:flex-row gap-4">
      <Button 
        variant="outline" 
        onClick={onRefresh}
        disabled={isRefreshing}
        className="flex items-center gap-2"
      >
        {isRefreshing ? (
          <>
            <RefreshCw className="h-4 w-4 animate-spin" />
            Refreshing...
          </>
        ) : (
          <>
            <RefreshCw className="h-4 w-4" />
            Refresh Data
          </>
        )}
      </Button>
      <Button variant="outline" onClick={() => navigate('/dashboard')}>
        Return to Dashboard
      </Button>
    </div>
  );

  return (
    <div className="p-8">
      <div className="mb-8 flex items-center gap-3">
        <ShieldAlert className="h-6 w-6 text-red-500" />
        <h1 className="text-3xl font-bold">Admin Dashboard</h1>
      </div>
      
      <ErrorState
        title="Error Loading Admin Data"
        message={`${error}\n\nThis could be due to a permissions issue or a database configuration problem.`}
        action={actions}
      />
    </div>
  );
};

export default AdminErrorState;
