import { useState, useCallback, useEffect, useRef } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/hooks/usePermissionsFixed';
import { toast } from 'sonner';
import { syncAllPropertiesCalendarsIfNeeded } from '@/utils/calendarUtils';

// Types
export interface Property {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  image_url: string;
  bedrooms: number;
  bathrooms: number;
  budget: number;
  ical_url: string;
  next_booking: string;
  next_checkin_date: string;
  next_checkin_formatted: string;
  collections: any[];
  is_occupied: boolean;
  current_checkout: string;
  last_ical_sync: string;
  timezone: string;
  check_in_time: string;
  check_out_time: string;
}

export interface MaintenanceTask {
  id: string;
  title: string;
  description: string;
  status: 'new' | 'in_progress' | 'completed' | 'cancelled';
  severity: 'low' | 'medium' | 'high' | 'critical';
  due_date: string;
  property_id: string;
  property_name: string;
  assigned_to: string | null;
  assigned_to_name: string | null;
  created_at: string;
  updated_at: string;
}

export interface InventoryItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
  min_quantity: number;
  property_id: string;
  property_name: string;
  category: string;
  image_url: string;
  created_at: string;
  updated_at: string;
}

export interface DamageReport {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'in_progress' | 'resolved';
  property_id: string;
  property_name: string;
  reported_by: string;
  reported_by_name: string;
  created_at: string;
  updated_at: string;
}

export interface PurchaseOrder {
  id: string;
  status: 'draft' | 'ordered' | 'received' | 'cancelled';
  total: number;
  created_at: string;
  updated_at: string;
  items: any[];
  property_id: string;
  property_name: string;
}

export interface MaintenanceTaskResult {
  tasks: MaintenanceTask[];
  loading: boolean;
  error: string | null;
  refreshTasks: () => Promise<void>;
}

export interface DashboardData {
  properties: Property[];
  maintenanceTaskResult: MaintenanceTaskResult;
  inventoryItems: InventoryItem[];
  damages: DamageReport[];
  purchaseOrders: PurchaseOrder[];
  pendingOrders: PurchaseOrder[];
  loading: boolean;
  error: string | null;
  refreshData: () => void;
}

export const useDashboardDataQuery = (): DashboardData => {
  const { authState } = useAuth();
  const userId = authState.user?.id;
  const queryClient = useQueryClient();
  const { isAdmin, getAccessibleTeams } = usePermissions();
  const accessibleTeams = getAccessibleTeams();
  const [retryCount, setRetryCount] = useState(0);
  const isRefreshingRef = useRef(false);

  // Debug logging
  console.log('[useDashboardDataQuery] Hook called with:', {
    userId,
    isAuthenticated: authState.isAuthenticated,
    isLoading: authState.isLoading,
    hasProfile: !!authState.profile
  });

  // Function to retry data fetching manually if needed
  const retryFetch = useCallback(async () => {
    if (isRefreshingRef.current) {
      console.log('[useDashboardDataQuery] Already refreshing, skipping manual refresh');
      return;
    }

    console.log('[useDashboardDataQuery] Manual refresh triggered');
    isRefreshingRef.current = true;

    // Use proper React Query invalidation without changing query keys
    await queryClient.invalidateQueries({ queryKey: ['dashboardProperties'] });
    await queryClient.invalidateQueries({ queryKey: ['dashboardMaintenanceTasks'] });
    await queryClient.invalidateQueries({ queryKey: ['dashboardInventoryItems'] });
    await queryClient.invalidateQueries({ queryKey: ['dashboardDamages'] });
    await queryClient.invalidateQueries({ queryKey: ['dashboardPurchaseOrders'] });

    // Reset refreshing flag after a delay to prevent rapid refreshes
    setTimeout(() => {
      isRefreshingRef.current = false;
    }, 3000);
  }, [queryClient]);

  // We're now relying on React Query's built-in refetchOnWindowFocus functionality
  // instead of custom visibility change handlers
  useEffect(() => {
    console.log('[useDashboardDataQuery] Using React Query\'s built-in refetchOnWindowFocus for data refreshing');

    // Set up a global function to manually refresh data if needed
    // This is used by other components that need to trigger a dashboard refresh
    (window as any).refreshDashboardData = retryFetch;

    return () => {
      // Clean up the global function
      delete (window as any).refreshDashboardData;
    };
  }, [retryFetch]);

  // Fetch properties
  const {
    data: properties = [],
    isLoading: isLoadingProperties,
    error: propertiesError
  } = useQuery({
    queryKey: ['dashboardProperties'],
    queryFn: async () => {
      console.log('[useDashboardDataQuery] Fetching properties');

      try {
        // Use the unified get_user_role_properties function that handles all user roles
        const { data: rpcData, error: rpcError } = await supabase.rpc(
          'get_user_role_properties',
          { p_user_id: userId }
        );

        if (!rpcError && rpcData && rpcData.length > 0) {
          console.log(`[useDashboardDataQuery] Successfully loaded ${rpcData.length} properties via RPC function`);
          return rpcData;
        }

        // Fallback to direct query
        const { data, error } = await supabase
          .from('properties')
          .select('*')
          .eq('user_id', userId);

        if (error) throw error;

        // Auto-sync property calendars if needed
        if (data && data.length > 0 && userId) {
          syncAllPropertiesCalendarsIfNeeded(data, userId)
            .then(() => {
              console.log('[useDashboardDataQuery] Auto-synced property calendars if needed');
            })
            .catch(error => {
              console.error('[useDashboardDataQuery] Error auto-syncing property calendars:', error);
            });
        }

        return data || [];
      } catch (err: any) {
        console.error('[useDashboardDataQuery] Error fetching properties:', err);
        throw err;
      }
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: true,
    refetchOnReconnect: true,
    enabled: !!userId,
    networkMode: 'always'
  });

  // Fetch maintenance tasks
  const {
    data: maintenanceTasks = [],
    isLoading: isLoadingTasks,
    error: tasksError,
    refetch: refreshTasks
  } = useQuery({
    queryKey: ['dashboardMaintenanceTasks'],
    queryFn: async () => {
      console.log('[useDashboardDataQuery] Fetching maintenance tasks');

      try {
        // Try RPC function first - this handles proper access control for all user roles
        const { data: rpcTasks, error: rpcError } = await supabase.rpc(
          'get_maintenance_tasks_for_user',
          { p_user_id: userId }
        );

        // IMPORTANT: Trust the RPC function result even if it's an empty array
        // This is especially important for service providers who should see 0 tasks
        // when they have no team access to properties
        if (!rpcError && rpcTasks !== null && rpcTasks !== undefined) {
          console.log(`[useDashboardDataQuery] Successfully loaded ${rpcTasks.length} tasks via RPC function`);
          return rpcTasks;
        }

        // Fallback to direct query
        let query = supabase
          .from('maintenance_tasks')
          .select('*, properties(name)');

        // Apply different filters based on user role
        if (isAdmin()) {
          // Admins see all tasks
          console.log('[useDashboardDataQuery] Admin fetching all maintenance tasks');
        } else if (accessibleTeams.includes('*')) {
          // Special case for wildcard access
          console.log('[useDashboardDataQuery] User has wildcard access to teams');
        } else if (accessibleTeams.length > 0) {
          // For team members, get team property IDs
          console.log('[useDashboardDataQuery] Fetching team property maintenance tasks');

          const { data: teamPropertiesData } = await supabase
            .from('team_properties')
            .select('property_id')
            .in('team_id', accessibleTeams);

          if (teamPropertiesData && teamPropertiesData.length > 0) {
            const propertyIds = teamPropertiesData.map(tp => tp.property_id);
            query = query.in('property_id', propertyIds);
          }
        } else {
          // Regular users see only their own tasks
          query = query.eq('user_id', userId);
        }

        const { data, error } = await query;

        if (error) throw error;

        return data || [];
      } catch (err: any) {
        console.error('[useDashboardDataQuery] Error fetching maintenance tasks:', err);
        throw err;
      }
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: true,
    refetchOnReconnect: true,
    enabled: !!userId,
    networkMode: 'always'
  });

  // Fetch inventory items
  const {
    data: inventoryItems = [],
    isLoading: isLoadingInventory,
    error: inventoryError
  } = useQuery({
    queryKey: ['dashboardInventoryItems'],
    queryFn: async () => {
      console.log('[useDashboardDataQuery] Fetching inventory items');

      try {
        let query = supabase
          .from('inventory_items')
          .select('*, properties(name)')
          .order('created_at', { ascending: false });

        // Apply different filters based on user role
        if (isAdmin()) {
          // Admins see all items
          console.log('[useDashboardDataQuery] Admin fetching all inventory items');
        } else if (accessibleTeams.includes('*')) {
          // Special case for wildcard access
          console.log('[useDashboardDataQuery] User has wildcard access to teams');
        } else if (accessibleTeams.length > 0) {
          // For team members, get team property IDs
          console.log('[useDashboardDataQuery] Fetching team property inventory items');

          const { data: teamPropertiesData } = await supabase
            .from('team_properties')
            .select('property_id')
            .in('team_id', accessibleTeams);

          if (teamPropertiesData && teamPropertiesData.length > 0) {
            const propertyIds = teamPropertiesData.map(tp => tp.property_id);
            query = query.in('property_id', propertyIds);
          }
        } else {
          // Regular users see only their own items
          const { data: userProperties } = await supabase
            .from('properties')
            .select('id')
            .eq('user_id', userId);

          if (userProperties && userProperties.length > 0) {
            const propertyIds = userProperties.map(p => p.id);
            query = query.in('property_id', propertyIds);
          } else {
            query = query.eq('user_id', userId);
          }
        }

        const { data, error } = await query;

        if (error) throw error;

        return data || [];
      } catch (err: any) {
        console.error('[useDashboardDataQuery] Error fetching inventory items:', err);
        throw err;
      }
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: true,
    refetchOnReconnect: true,
    enabled: !!userId,
    networkMode: 'always'
  });

  // Fetch damage reports
  const {
    data: damages = [],
    isLoading: isLoadingDamages,
    error: damagesError
  } = useQuery({
    queryKey: ['dashboardDamages'],
    queryFn: async () => {
      console.log('[useDashboardDataQuery] Fetching damage reports');

      try {
        let query = supabase
          .from('damage_reports')
          .select('*, properties(name)')
          .order('created_at', { ascending: false });

        // Apply different filters based on user role
        if (isAdmin()) {
          // Admins see all damage reports
          console.log('[useDashboardDataQuery] Admin fetching all damage reports');
        } else if (accessibleTeams.includes('*')) {
          // Special case for wildcard access
          console.log('[useDashboardDataQuery] User has wildcard access to teams');
        } else if (accessibleTeams.length > 0) {
          // For team members, get team property IDs
          console.log('[useDashboardDataQuery] Fetching team property damage reports');

          const { data: teamPropertiesData } = await supabase
            .from('team_properties')
            .select('property_id')
            .in('team_id', accessibleTeams);

          if (teamPropertiesData && teamPropertiesData.length > 0) {
            const propertyIds = teamPropertiesData.map(tp => tp.property_id);
            query = query.in('property_id', propertyIds);
          }
        } else {
          // Regular users see only their own damage reports
          const { data: userProperties } = await supabase
            .from('properties')
            .select('id')
            .eq('user_id', userId);

          if (userProperties && userProperties.length > 0) {
            const propertyIds = userProperties.map(p => p.id);
            query = query.in('property_id', propertyIds);
          } else {
            query = query.eq('user_id', userId);
          }
        }

        const { data, error } = await query;

        if (error) throw error;

        const formattedDamages = (data || []).map(damage => ({
          id: damage.id,
          title: damage.title,
          description: damage.description,
          status: damage.status,
          property_id: damage.property_id,
          property_name: damage.properties?.name || 'Unknown Property',
          reported_by: damage.reported_by,
          reported_by_name: damage.reported_by_name,
          created_at: damage.created_at,
          updated_at: damage.updated_at
        }));

        return formattedDamages;
      } catch (err: any) {
        console.error('[useDashboardDataQuery] Error fetching damage reports:', err);
        throw err;
      }
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: true,
    refetchOnReconnect: true,
    enabled: !!userId,
    networkMode: 'always'
  });

  // Fetch purchase orders
  const {
    data: allPurchaseOrders = [],
    isLoading: isLoadingOrders,
    error: ordersError
  } = useQuery({
    queryKey: ['dashboardPurchaseOrders'],
    queryFn: async () => {
      console.log('[useDashboardDataQuery] Fetching purchase orders');

      try {
        let query = supabase
          .from('purchase_orders')
          .select('*, purchase_order_items(*), properties(name)')
          .order('created_at', { ascending: false });

        // Apply different filters based on user role
        if (isAdmin()) {
          // Admins see all orders
          console.log('[useDashboardDataQuery] Admin fetching all purchase orders');
        } else if (accessibleTeams.includes('*')) {
          // Special case for wildcard access
          console.log('[useDashboardDataQuery] User has wildcard access to teams');
        } else if (accessibleTeams.length > 0) {
          // For team members, get team property IDs
          console.log('[useDashboardDataQuery] Fetching team property purchase orders');

          const { data: teamPropertiesData } = await supabase
            .from('team_properties')
            .select('property_id')
            .in('team_id', accessibleTeams);

          if (teamPropertiesData && teamPropertiesData.length > 0) {
            const propertyIds = teamPropertiesData.map(tp => tp.property_id);
            query = query.in('property_id', propertyIds);
          }
        } else {
          // Regular users see only their own orders
          const { data: userProperties } = await supabase
            .from('properties')
            .select('id')
            .eq('user_id', userId);

          if (userProperties && userProperties.length > 0) {
            const propertyIds = userProperties.map(p => p.id);
            query = query.in('property_id', propertyIds);
          } else {
            query = query.eq('user_id', userId);
          }
        }

        const { data, error } = await query;

        if (error) throw error;

        const formattedOrders = (data || []).map(order => ({
          id: order.id,
          status: order.status,
          total: order.total,
          created_at: order.created_at,
          updated_at: order.updated_at,
          items: order.purchase_order_items,
          property_id: order.property_id,
          property_name: order.properties?.name || 'Unknown Property'
        }));

        return formattedOrders;
      } catch (err: any) {
        console.error('[useDashboardDataQuery] Error fetching purchase orders:', err);
        throw err;
      }
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: true,
    refetchOnReconnect: true,
    enabled: !!userId,
    networkMode: 'always'
  });

  // Filter pending orders
  const purchaseOrders = allPurchaseOrders || [];
  const pendingOrders = purchaseOrders.filter(order => order.status === 'ordered');

  // Handle auto-retry for errors using useEffect for better cleanup
  const hasError = !!(propertiesError || tasksError || inventoryError || damagesError || ordersError);

  // Add error retry effect similar to usePurchaseOrders
  useEffect(() => {
    if (hasError) {
      console.log('[useDashboardDataQuery] Error detected, scheduling auto-retry');
      const timer = setTimeout(() => {
        if (retryCount < 3) {
          console.log('[useDashboardDataQuery] Auto-retrying data fetch');
          retryFetch();
        } else {
          console.error('[useDashboardDataQuery] Failed to load dashboard data after multiple attempts');
        }
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [hasError, retryCount, retryFetch]);

  // Create the maintenance task result object
  const maintenanceTaskResult: MaintenanceTaskResult = {
    tasks: maintenanceTasks || [],
    loading: isLoadingTasks,
    error: tasksError ? String(tasksError) : null,
    refreshTasks: async () => {
      await refreshTasks();
    }
  };

  return {
    properties,
    maintenanceTaskResult,
    inventoryItems,
    damages,
    purchaseOrders,
    pendingOrders,
    loading: isLoadingProperties || isLoadingTasks || isLoadingInventory || isLoadingDamages || isLoadingOrders,
    error: hasError ? 'Error loading dashboard data' : null,
    refreshData: retryFetch
  };
};
