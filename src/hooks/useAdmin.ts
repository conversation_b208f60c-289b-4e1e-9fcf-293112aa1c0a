import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { UserProfile, UserRole } from '@/types/auth';
import { toast } from 'sonner';

/**
 * Hook for admin functionality
 * Centralizes admin operations and provides reusable functions
 */
export const useAdmin = () => {
  const { authState } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Check if the current user is a super admin
   */
  const isSuperAdmin = useCallback(() => {
    return !!authState?.profile?.is_super_admin;
  }, [authState?.profile?.is_super_admin]);

  /**
   * Fetch all users from the system
   */
  const fetchUsers = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) throw sessionError;
      
      const token = sessionData.session?.access_token;
      if (!token) throw new Error('Authentication required');

      const { data, error } = await supabase.functions.invoke('admin-get-users', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (error) throw error;
      if (!data || !Array.isArray(data.users)) {
        throw new Error('Invalid response format from admin-get-users');
      }

      return data.users;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to fetch users';
      setError(errorMessage);
      console.error('Error fetching users:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Update a user's profile
   */
  const updateUser = useCallback(async (
    userId: string, 
    userData: { 
      first_name?: string; 
      last_name?: string; 
      email?: string; 
      role?: UserRole;
      is_super_admin?: boolean;
    }
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) throw sessionError;
      
      const token = sessionData.session?.access_token;
      if (!token) throw new Error('Authentication required');

      // Call the admin-update-user function (assuming it exists)
      const { error } = await supabase.functions.invoke('admin-update-user', {
        headers: { Authorization: `Bearer ${token}` },
        body: { 
          userId,
          userData
        }
      });

      if (error) throw error;
      
      toast.success('User updated successfully');
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to update user';
      setError(errorMessage);
      toast.error(errorMessage);
      console.error('Error updating user:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Delete a user
   */
  const deleteUser = useCallback(async (userId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) throw sessionError;
      
      const token = sessionData.session?.access_token;
      if (!token) throw new Error('Authentication required');

      // Call the admin-delete-user function (assuming it exists)
      const { error } = await supabase.functions.invoke('admin-delete-user', {
        headers: { Authorization: `Bearer ${token}` },
        body: { userId }
      });

      if (error) throw error;
      
      toast.success('User deleted successfully');
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to delete user';
      setError(errorMessage);
      toast.error(errorMessage);
      console.error('Error deleting user:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Force delete a user (for corrupted accounts)
   */
  const forceDeleteUser = useCallback(async (userId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) throw sessionError;
      
      const token = sessionData.session?.access_token;
      if (!token) throw new Error('Authentication required');

      // Call the admin-force-delete-user function
      const { error } = await supabase.functions.invoke('admin-force-delete-user', {
        headers: { Authorization: `Bearer ${token}` },
        body: { userId }
      });

      if (error) throw error;
      
      toast.success('User force deleted successfully');
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to force delete user';
      setError(errorMessage);
      toast.error(errorMessage);
      console.error('Error force deleting user:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Reset a user's password
   */
  const resetUserPassword = useCallback(async (userId: string, newPassword: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) throw sessionError;
      
      const token = sessionData.session?.access_token;
      if (!token) throw new Error('Authentication required');

      // TODO: Implement admin-reset-password function
      // For now, use Supabase Auth Admin API directly
      console.warn('admin-reset-password function not implemented, using fallback');
      const { error } = await supabase.auth.admin.updateUserById(userId, {
        password: newPassword
      });

      if (error) throw error;
      
      toast.success('Password reset successfully');
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to reset password';
      setError(errorMessage);
      toast.error(errorMessage);
      console.error('Error resetting password:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Create a database backup
   */
  const createDatabaseBackup = useCallback(async (backupName: string, format: 'json' | 'sql' = 'json') => {
    setIsLoading(true);
    setError(null);

    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) throw sessionError;
      
      const token = sessionData.session?.access_token;
      if (!token) throw new Error('Authentication required');
      
      const { data, error } = await supabase.functions.invoke('admin-backup-database', {
        headers: {
          Authorization: `Bearer ${token}`
        },
        body: { 
          backupName,
          format
        }
      });
      
      if (error) throw error;
      
      return data;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to create database backup';
      setError(errorMessage);
      console.error('Error creating database backup:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Impersonate a user
   */
  const impersonateUser = useCallback(async (userId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // This function should be implemented in the AuthContext
      // We're just providing a wrapper here for consistency
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) throw sessionError;
      
      const token = sessionData.session?.access_token;
      if (!token) throw new Error('Authentication required');

      // TODO: Implement admin-impersonate-user function
      // For now, return a placeholder response
      console.warn('admin-impersonate-user function not implemented, using placeholder');
      const error = null;
      const data = { 
        message: 'Impersonation feature not implemented yet',
        userId: userId
      };

      if (error) throw error;
      
      // Store the impersonation token and refresh the page
      if (data && data.token) {
        localStorage.setItem('supabase.auth.token.impersonated', data.token);
        localStorage.setItem('original_user_id', authState?.user?.id || '');
        
        // Reload to apply the new token
        window.location.href = '/dashboard';
        return true;
      } else {
        throw new Error('Invalid response from impersonation service');
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to impersonate user';
      setError(errorMessage);
      console.error('Error impersonating user:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [authState?.user?.id]);

  return {
    isLoading,
    error,
    isSuperAdmin,
    fetchUsers,
    updateUser,
    deleteUser,
    forceDeleteUser,
    resetUserPassword,
    createDatabaseBackup,
    impersonateUser
  };
};

export default useAdmin;
