import { useState, useCallback, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useImpersonation } from '@/contexts/ImpersonationContext';
import { MaintenanceTask, MaintenanceTaskFilters } from '@/components/maintenance/types';
import { toast } from 'sonner';

export interface MaintenanceTasksData {
  tasks: MaintenanceTask[];
  loading: boolean;
  error: string | null;
  isError: boolean;
  fetchTasks: () => Promise<void>;
  updateTaskStatus: (taskId: string, status: string) => Promise<boolean>;
  deleteTask: (taskId: string) => Promise<boolean>;
  addTask: (task: Omit<MaintenanceTask, 'id' | 'status' | 'createdAt'>) => Promise<any>;
  updateTask: (taskId: string, task: Omit<MaintenanceTask, 'id' | 'createdAt'>) => Promise<any>;
}

/**
 * A standardized hook for fetching maintenance tasks using React Query
 * This follows the same pattern as useOperationsDataQuery for consistency
 */
export const useMaintenanceTasksQueryV2 = (
  initialFilters: Partial<MaintenanceTaskFilters> = {}
): MaintenanceTasksData => {
  const { authState } = useAuth();
  const userId = authState?.user?.id;
  const queryClient = useQueryClient();
  const [retryCount, setRetryCount] = useState(0);
  const { isImpersonating } = useImpersonation();

  // Debug auth state
  console.log('[useMaintenanceTasksQueryV2] Auth state:', {
    authState,
    userId,
    isAuthenticated: authState?.isAuthenticated,
    isLoading: authState?.isLoading,
    user: authState?.user,
    userEmail: authState?.user?.email,
    session: authState?.session
  });

  // Function to retry data fetching manually
  const retryFetch = useCallback(async () => {
    console.log('[useMaintenanceTasksQueryV2] Manual refresh triggered');
    setRetryCount(prev => prev + 1);
    await queryClient.invalidateQueries({ queryKey: ['maintenanceTasksV2'] });
    await queryClient.refetchQueries({ queryKey: ['maintenanceTasksV2'] });
  }, [queryClient]);

  // Keep track of previous tasks to prevent data disappearing during loading
  const [previousTasks, setPreviousTasks] = useState<MaintenanceTask[]>([]);

  // Fetch maintenance tasks
  const {
    data: tasks = [],
    isLoading,
    error,
    isError
  } = useQuery({
    queryKey: ['maintenanceTasksV2'],
    enabled: !!userId && !authState?.isLoading && authState?.isAuthenticated, // Only run when user is authenticated and not loading
    retry: (failureCount, error) => {
      console.log('[useMaintenanceTasksQueryV2] Query failed:', error?.message, 'Attempt:', failureCount + 1);

      // Retry if it's an auth error, network error, or we haven't tried too many times
      if (failureCount < 3) {
        if (error?.message?.includes('User not authenticated') ||
            error?.message?.includes('Failed to fetch') ||
            error?.message?.includes('TypeError: Failed to fetch')) {
          console.log('[useMaintenanceTasksQueryV2] Retrying due to error, attempt:', failureCount + 1);
          return true;
        }
      }
      return false;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    queryFn: async () => {
      try {
        console.log('[useMaintenanceTasksQueryV2] Query starting with userId:', userId, 'authState:', authState?.isAuthenticated);

        if (!userId || !authState?.isAuthenticated) {
          throw new Error('User not authenticated');
        }

        console.log(`[useMaintenanceTasksQueryV2] Fetching tasks (attempt ${retryCount + 1})`);

        // Use our RPC function to get tasks
        const { data: rpcTasks, error: rpcError } = await supabase.rpc('get_maintenance_tasks_for_user', {
          p_user_id: userId
        });

        if (rpcError) {
          console.error('[useMaintenanceTasksQueryV2] RPC function error:', rpcError);
          throw rpcError;
        }

        if (!rpcTasks || rpcTasks.length === 0) {
          console.log('[useMaintenanceTasksQueryV2] No tasks found');
          return previousTasks.length > 0 ? previousTasks : []; // Return previous tasks if available
        }

        console.log(`[useMaintenanceTasksQueryV2] Successfully loaded ${rpcTasks.length} tasks`);
        console.log(`[useMaintenanceTasksQueryV2] Raw RPC tasks:`, rpcTasks);

        // Apply impersonation filter if needed
        let filteredTasks = rpcTasks;
        if (isImpersonating) {
          console.log('[useMaintenanceTasksQueryV2] Impersonating user, filtering tasks');
          filteredTasks = rpcTasks.filter(task => task.user_id === userId);
        }

        // Get user's role from profiles table for proper filtering
        const { data: userProfile } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', userId)
          .single();
        
        const userRole = userProfile?.role;
        console.log('[useMaintenanceTasksQueryV2] User role:', userRole);

        // Additional filtering for service providers to ensure proper access control
        // This is a client-side safety check until the database function is properly fixed
        if (userRole === 'service_provider') {
          console.log('[useMaintenanceTasksQueryV2] Additional service provider filtering');
          
          // Get user's team memberships to validate access
          const { data: userTeams } = await supabase
            .from('team_members')
            .select('team_id')
            .eq('user_id', userId);
          
          const userTeamIds = userTeams?.map(tm => tm.team_id) || [];
          console.log('[useMaintenanceTasksQueryV2] User team IDs:', userTeamIds);
          
          // Get properties user has access to through team membership
          const { data: accessibleProperties } = await supabase
            .from('team_properties')
            .select('property_id')
            .in('team_id', userTeamIds);
          
          const accessiblePropertyIds = accessibleProperties?.map(tp => tp.property_id) || [];
          console.log('[useMaintenanceTasksQueryV2] Accessible property IDs:', accessiblePropertyIds);
          
          // Filter tasks to only include those the service provider should access
          filteredTasks = filteredTasks.filter(task => {
            // Allow if user created the task
            if (task.user_id === userId) {
              return true;
            }

            // Allow if task is assigned to them AND they have team access to the property
            if ((task.assigned_to === userId || task.provider_id === userId)) {
              // Check if task has a team they're a member of
              if (task.team_id && userTeamIds.includes(task.team_id)) {
                return true;
              }

              // Check if task property is accessible through team membership
              if (task.property_id && accessiblePropertyIds.includes(task.property_id)) {
                return true;
              }

              // If no team/property access, deny
              console.log(`[useMaintenanceTasksQueryV2] Filtering out task "${task.title}" - no team access`);
              return false;
            }

            // Allow if user is a team member with access to the task's team/property (even if not assigned)
            if (task.team_id && userTeamIds.includes(task.team_id)) {
              console.log(`[useMaintenanceTasksQueryV2] Allowing task "${task.title}" - user is team member`);
              return true;
            }

            if (task.property_id && accessiblePropertyIds.includes(task.property_id)) {
              console.log(`[useMaintenanceTasksQueryV2] Allowing task "${task.title}" - user has property access`);
              return true;
            }

            // Deny all other tasks
            console.log(`[useMaintenanceTasksQueryV2] Filtering out task "${task.title}" - no access`);
            return false;
          });
          
          console.log(`[useMaintenanceTasksQueryV2] Filtered from ${rpcTasks.length} to ${filteredTasks.length} tasks for service provider`);
        }

        // Format tasks
        const formattedTasks = filteredTasks.map((task: any) => ({
          id: task.id,
          title: task.title || '',
          description: task.description || '',
          status: task.status || 'open',
          severity: task.severity || 'medium',
          dueDate: task.due_date || '',
          propertyId: task.property_id || '',
          propertyName: task.property_name || '',
          assignedTo: task.assigned_to || '',
          providerId: task.provider_id || '',
          providerName: task.provider_name || '',
          createdAt: task.created_at || '',
          updatedAt: task.updated_at || '',
          userId: task.user_id || '',
          // Recurring task properties
          isRecurring: task.is_recurring || false,
          recurrenceIntervalDays: task.recurrence_interval_days,
          parentTaskId: task.parent_task_id,
          nextDueDate: task.next_due_date,
          recurrenceCount: task.recurrence_count || 0,
          maxRecurrences: task.max_recurrences,
          completedAt: task.completed_at,
        }));

        // Update previous tasks for future use
        setPreviousTasks(formattedTasks);

        return formattedTasks;
      } catch (err: any) {
        console.error('[useMaintenanceTasksQueryV2] Error fetching tasks:', err);
        // Return previous tasks on error to prevent data disappearing
        if (previousTasks.length > 0) {
          console.log('[useMaintenanceTasksQueryV2] Returning previous tasks due to error');
          return previousTasks;
        }
        throw err;
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: true,
    refetchOnReconnect: true,
    keepPreviousData: true, // Keep previous data while fetching new data
    placeholderData: previousTasks.length > 0 ? previousTasks : undefined, // Use previous tasks as placeholder data
    networkMode: 'always'
  });

  // Update task status
  const updateTaskStatus = async (taskId: string, status: string): Promise<boolean> => {
    if (!userId) {
      console.error('[useMaintenanceTasksQueryV2] No authenticated user for status update');
      toast.error('User not authenticated');
      return false;
    }

    try {
      console.log(`[useMaintenanceTasksQueryV2] Updating task ${taskId} status to ${status}`);

      // Check if this is a recurring task being completed
      let isRecurringTask = false;
      if (status === 'completed') {
        const { data: taskData } = await supabase
          .from('maintenance_tasks')
          .select('is_recurring, title')
          .eq('id', taskId)
          .single();

        isRecurringTask = taskData?.is_recurring || false;
      }

      const { error } = await supabase
        .from('maintenance_tasks')
        .update({ status, updated_at: new Date().toISOString() })
        .eq('id', taskId);

      if (error) {
        console.error('[useMaintenanceTasksQueryV2] Supabase error updating task status:', error);
        toast.error(`Failed to update task status: ${error.message}`);
        throw error;
      }

      console.log(`[useMaintenanceTasksQueryV2] Successfully updated task ${taskId} status to ${status}`);

      // Show appropriate success message
      if (status === 'completed' && isRecurringTask) {
        toast.success('Task completed! Next occurrence has been scheduled.');
      }

      // Invalidate and refetch tasks
      await queryClient.invalidateQueries({ queryKey: ['maintenanceTasksV2'] });
      await retryFetch();

      return true;
    } catch (error: any) {
      console.error('[useMaintenanceTasksQueryV2] Exception updating task status:', error);
      if (!error.message?.includes('Failed to update task status:')) {
        toast.error(`Failed to update task status: ${error.message || 'Unknown error'}`);
      }
      return false;
    }
  };

  // Delete task
  const deleteTask = async (taskId: string): Promise<boolean> => {
    if (!userId) {
      console.error('[useMaintenanceTasksQueryV2] No authenticated user for task deletion');
      toast.error('User not authenticated');
      return false;
    }

    try {
      console.log(`[useMaintenanceTasksQueryV2] Deleting task ${taskId}`);

      const { error } = await supabase
        .from('maintenance_tasks')
        .delete()
        .eq('id', taskId);

      if (error) {
        console.error('[useMaintenanceTasksQueryV2] Supabase error deleting task:', error);
        toast.error(`Failed to delete task: ${error.message}`);
        throw error;
      }

      console.log(`[useMaintenanceTasksQueryV2] Successfully deleted task ${taskId}`);

      // Invalidate and refetch tasks
      await queryClient.invalidateQueries({ queryKey: ['maintenanceTasksV2'] });
      await retryFetch();

      return true;
    } catch (error: any) {
      console.error('[useMaintenanceTasksQueryV2] Exception deleting task:', error);
      if (!error.message?.includes('Failed to delete task:')) {
        toast.error(`Failed to delete task: ${error.message || 'Unknown error'}`);
      }
      return false;
    }
  };

  // Add task
  const addTask = async (task: Omit<MaintenanceTask, 'id' | 'status' | 'createdAt'>): Promise<any> => {
    if (!userId) {
      console.error('[useMaintenanceTasksQueryV2] No authenticated user');
      toast.error('User not authenticated');
      return false;
    }

    try {
      console.log('[useMaintenanceTasksQueryV2] Adding task:', task);
      console.log('[useMaintenanceTasksQueryV2] User ID:', userId);

      // Validate and determine property access
      let validPropertyId: string | null = null;
      let teamId: string | null = null;

      if (task.propertyId) {
        // First check if the property exists and user has access to it
        const { data: propertyData, error: propertyError } = await supabase
          .from('properties')
          .select('id, user_id, team_id')
          .eq('id', task.propertyId)
          .limit(1);

        if (propertyError) {
          console.warn(`[useMaintenanceTasksQueryV2] Error checking property access: ${propertyError.message}`);
        } else if (propertyData && propertyData.length > 0) {
          const property = propertyData[0];

          // Check if user owns the property or has team access
          if (property.user_id === userId) {
            validPropertyId = task.propertyId;
            teamId = property.team_id;
            console.log(`[useMaintenanceTasksQueryV2] User owns property ${task.propertyId}, team_id: ${teamId}`);
          } else {
            // Check if user has team access to this property
            // First get team_properties for this property
            const { data: teamPropertyData, error: teamPropertyError } = await supabase
              .from('team_properties')
              .select('team_id')
              .eq('property_id', task.propertyId);

            if (teamPropertyError) {
              console.error(`[useMaintenanceTasksQueryV2] Error fetching team properties:`, teamPropertyError);
            }

            // Then check if user is a member of any of these teams
            if (teamPropertyData && teamPropertyData.length > 0) {
              const teamIds = teamPropertyData.map((tp: any) => tp.team_id);

              const { data: teamMemberData, error: teamMemberError } = await supabase
                .from('team_members')
                .select('team_id')
                .in('team_id', teamIds)
                .eq('user_id', userId)
                .eq('status', 'active')
                .limit(1);

              if (!teamMemberError && teamMemberData && teamMemberData.length > 0) {
                teamId = teamMemberData[0].team_id;
                validPropertyId = task.propertyId;
                console.log(`[useMaintenanceTasksQueryV2] User has team access to property ${task.propertyId} via team ${teamId}`);
              }
            }

            // If user still doesn't have access, log warning
            if (!validPropertyId) {
              console.warn(`[useMaintenanceTasksQueryV2] User does not have access to property ${task.propertyId}, creating task without property association`);
            }
          }
        } else {
          console.warn(`[useMaintenanceTasksQueryV2] Property ${task.propertyId} not found, creating task without property association`);
        }
      }

      const dbTask = {
        title: task.title,
        description: task.description,
        property_id: validPropertyId,
        property_name: task.propertyName,
        severity: task.severity,
        status: 'open',
        due_date: task.dueDate === 'No due date' ? null : task.dueDate,
        user_id: userId,
        provider_id: task.providerId || null,
        provider_email: task.providerEmail || null,
        assigned_to: task.assignedTo || null,
        team_id: teamId,
        // Add recurring task fields if present
        is_recurring: (task as any).isRecurring || false,
        recurrence_interval_days: (task as any).recurrenceIntervalDays || null,
        max_recurrences: (task as any).maxRecurrences || null,
        parent_task_id: (task as any).parentTaskId || null,
        recurrence_count: (task as any).recurrenceCount || null,
        next_due_date: (task as any).nextDueDate || null
      };

      console.log('[useMaintenanceTasksQueryV2] Final task data:', {
        ...dbTask,
        validPropertyId,
        teamId,
        originalPropertyId: task.propertyId
      });
      console.log('[useMaintenanceTasksQueryV2] Sending to Supabase:', dbTask);

      // Check current auth state before insert
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      console.log('[useMaintenanceTasksQueryV2] Current auth user:', user?.id, user?.email);
      console.log('[useMaintenanceTasksQueryV2] Auth error:', authError);

      // Also check the session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      console.log('[useMaintenanceTasksQueryV2] Current session:', session?.user?.id, session?.user?.email);
      console.log('[useMaintenanceTasksQueryV2] Session error:', sessionError);
      console.log('[useMaintenanceTasksQueryV2] Session access token exists:', !!session?.access_token);
      console.log('[useMaintenanceTasksQueryV2] Session expires at:', session?.expires_at);

      // If no session, try to refresh
      if (!session?.access_token) {
        console.log('[useMaintenanceTasksQueryV2] No access token, attempting to refresh session...');
        const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
        console.log('[useMaintenanceTasksQueryV2] Refresh result:', refreshData?.session?.user?.id, refreshError);
      }

      const { data, error } = await supabase
        .from('maintenance_tasks')
        .insert(dbTask)
        .select();

      if (error) {
        console.error('[useMaintenanceTasksQueryV2] Supabase error:', error);

        // Provide more specific error messages
        if (error.code === '42501') {
          throw new Error('Permission denied: You do not have access to create maintenance tasks for this property');
        } else if (error.code === '23503') {
          throw new Error('Invalid property or team reference');
        } else if (error.code === '23502') {
          throw new Error('Missing required field');
        } else {
          throw new Error(`Database error: ${error.message}`);
        }
      }

      console.log('[useMaintenanceTasksQueryV2] Task added successfully:', data);

      // Invalidate and refetch tasks
      await queryClient.invalidateQueries({ queryKey: ['maintenanceTasksV2'] });
      await retryFetch();

      // Return the created task data for email notifications
      if (data && data.length > 0) {
        const createdTask = {
          id: data[0].id,
          title: data[0].title,
          description: data[0].description,
          propertyId: data[0].property_id,
          propertyName: data[0].property_name,
          severity: data[0].severity,
          status: data[0].status,
          dueDate: data[0].due_date || 'No due date',
          assignedTo: data[0].assigned_to,
          providerId: data[0].provider_id,
          providerEmail: data[0].provider_email,
          createdAt: data[0].created_at
        };
        return createdTask;
      }

      return true;
    } catch (error: any) {
      console.error('[useMaintenanceTasksQueryV2] Error adding task:', error);
      toast.error(`Failed to add task: ${error.message || 'Unknown error'}`);
      return false;
    }
  };

  // Update task
  const updateTask = async (taskId: string, task: Omit<MaintenanceTask, 'id' | 'createdAt'>): Promise<any> => {
    if (!userId) return false;

    try {
      console.log('[useMaintenanceTasksQueryV2] Updating task:', taskId, task);

      const dbTask = {
        title: task.title,
        description: task.description,
        property_id: task.propertyId || null,
        property_name: task.propertyName,
        severity: task.severity,
        status: task.status,
        due_date: task.dueDate === 'No due date' ? null : task.dueDate,
        provider_id: task.providerId || null,
        provider_email: task.providerEmail || null,
        assigned_to: task.assignedTo || null,
        updated_at: new Date().toISOString(),
        // Recurring task properties
        is_recurring: task.isRecurring || false,
        recurrence_interval_days: task.recurrenceIntervalDays || null,
        parent_task_id: task.parentTaskId || null,
        next_due_date: task.nextDueDate ? new Date(task.nextDueDate).toISOString() : null,
        recurrence_count: task.recurrenceCount || 0,
        max_recurrences: task.maxRecurrences || null
      };

      console.log('[useMaintenanceTasksQueryV2] Sending update to Supabase:', dbTask);

      const { data, error } = await supabase
        .from('maintenance_tasks')
        .update(dbTask)
        .eq('id', taskId)
        .select();

      if (error) {
        console.error('[useMaintenanceTasksQueryV2] Supabase error:', error);
        throw error;
      }

      console.log('[useMaintenanceTasksQueryV2] Task updated successfully:', data);

      // Invalidate and refetch tasks
      await queryClient.invalidateQueries({ queryKey: ['maintenanceTasksV2'] });
      await retryFetch();

      // Return the updated task data for email notifications
      if (data && data.length > 0) {
        const updatedTask = {
          id: data[0].id,
          title: data[0].title,
          description: data[0].description,
          propertyId: data[0].property_id,
          propertyName: data[0].property_name,
          severity: data[0].severity,
          status: data[0].status,
          dueDate: data[0].due_date || 'No due date',
          assignedTo: data[0].assigned_to,
          providerId: data[0].provider_id,
          providerEmail: data[0].provider_email,
          createdAt: data[0].created_at
        };
        return updatedTask;
      }

      return true;
    } catch (error: any) {
      console.error('[useMaintenanceTasksQueryV2] Error updating task:', error);
      toast.error(`Failed to update task: ${error.message || 'Unknown error'}`);
      return false;
    }
  };

  // Add error retry effect similar to usePurchaseOrders
  useEffect(() => {
    // Only retry on actual errors, not on empty data
    if (isError) {
      const timer = setTimeout(() => {
        if (retryCount < 3) {
          console.log('[useMaintenanceTasksQueryV2] Auto-retrying data fetch due to error');
          retryFetch();
        } else {
          console.error('[useMaintenanceTasksQueryV2] Failed to load tasks after multiple attempts');
        }
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [isError, retryCount, retryFetch]);

  return {
    tasks,
    loading: isLoading,
    error: error ? String(error) : null,
    isError,
    fetchTasks: retryFetch,
    updateTaskStatus,
    deleteTask,
    addTask,
    updateTask
  };
};
