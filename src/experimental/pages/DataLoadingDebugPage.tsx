import React, { useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useNavigationRefresh } from '@/hooks/useNavigationRefresh';
import DataLoadingDebugger from '@/components/debug/DataLoadingDebugger';
import DataLoadingTestComponent from '@/components/debug/DataLoadingTestComponent';
import { forceRefreshQueries } from '@/utils/debugUtils';

/**
 * A debug page for diagnosing and fixing data loading issues
 */
const DataLoadingDebugPage: React.FC = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { refreshRouteData } = useNavigationRefresh();
  const [customRoute, setCustomRoute] = useState('');
  const [customQueryKey, setCustomQueryKey] = useState('');
  const [activeTab, setActiveTab] = useState('routes');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Define problematic routes
  const problematicRoutes = [
    '/properties/375c8d62-63af-4fd7-90b3-b3c3f2efbec6',
    '/maintenance',
    '/damages',
    '/teams',
    '/settings/appearance'
  ];

  // Define working routes
  const workingRoutes = [
    '/dashboard',
    '/operations',
    '/properties',
    '/maintenance/automation',
    '/purchase-orders'
  ];

  // Navigate to a route
  const handleNavigate = (route: string) => {
    navigate(route);
  };

  // Refresh a route
  const handleRefreshRoute = async (route: string) => {
    setIsRefreshing(true);
    try {
      await refreshRouteData(route);
    } catch (error) {
      console.error('Error refreshing route data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Refresh a query key
  const handleRefreshQueryKey = async (queryKey: string) => {
    setIsRefreshing(true);
    try {
      await forceRefreshQueries(queryClient, [queryKey]);
    } catch (error) {
      console.error('Error refreshing query key:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Invalidate all queries
  const handleInvalidateAll = async () => {
    setIsRefreshing(true);
    try {
      await queryClient.invalidateQueries();
      await queryClient.refetchQueries({ type: 'all' });
    } catch (error) {
      console.error('Error invalidating all queries:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <h1 className="text-2xl font-bold">Data Loading Debug Page</h1>
      <p className="text-gray-600">
        Use this page to diagnose and fix data loading issues when returning from out-of-focus.
      </p>

      <DataLoadingDebugger />

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="test">Test Suite</TabsTrigger>
          <TabsTrigger value="routes">Routes</TabsTrigger>
          <TabsTrigger value="queries">Query Keys</TabsTrigger>
          <TabsTrigger value="custom">Custom</TabsTrigger>
        </TabsList>

        <TabsContent value="test">
          <DataLoadingTestComponent />
        </TabsContent>

        <TabsContent value="routes" className="space-y-4">
          <Card className="p-4">
            <h2 className="text-lg font-semibold mb-2">Problematic Routes</h2>
            <p className="text-sm text-gray-600 mb-4">
              These routes have issues with data loading when returning from out-of-focus.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {problematicRoutes.map((route) => (
                <div key={route} className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    onClick={() => handleNavigate(route)}
                    className="flex-1"
                  >
                    Navigate to {route}
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={() => handleRefreshRoute(route)}
                    disabled={isRefreshing}
                    className="flex-shrink-0"
                  >
                    Refresh
                  </Button>
                </div>
              ))}
            </div>
          </Card>

          <Card className="p-4">
            <h2 className="text-lg font-semibold mb-2">Working Routes</h2>
            <p className="text-sm text-gray-600 mb-4">
              These routes work correctly when returning from out-of-focus.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {workingRoutes.map((route) => (
                <div key={route} className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    onClick={() => handleNavigate(route)}
                    className="flex-1"
                  >
                    Navigate to {route}
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={() => handleRefreshRoute(route)}
                    disabled={isRefreshing}
                    className="flex-shrink-0"
                  >
                    Refresh
                  </Button>
                </div>
              ))}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="queries" className="space-y-4">
          <Card className="p-4">
            <h2 className="text-lg font-semibold mb-2">Common Query Keys</h2>
            <p className="text-sm text-gray-600 mb-4">
              Refresh specific query keys to fix data loading issues.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {[
                'properties',
                'propertiesV2',
                'propertyDetail',
                'maintenanceTasks',
                'maintenanceTasksV2',
                'damageReports',
                'damageReportsV2',
                'teamsV2',
                'teamMembersV2',
                'teamPermissionsV2',
                'appearanceSettingsV2'
              ].map((queryKey) => (
                <Button
                  key={queryKey}
                  variant="outline"
                  onClick={() => handleRefreshQueryKey(queryKey)}
                  disabled={isRefreshing}
                >
                  Refresh {queryKey}
                </Button>
              ))}
            </div>
          </Card>

          <Card className="p-4">
            <h2 className="text-lg font-semibold mb-2">Global Actions</h2>
            <Button
              variant="default"
              onClick={handleInvalidateAll}
              disabled={isRefreshing}
              className="w-full"
            >
              Invalidate All Queries
            </Button>
          </Card>
        </TabsContent>

        <TabsContent value="custom" className="space-y-4">
          <Card className="p-4">
            <h2 className="text-lg font-semibold mb-2">Custom Route</h2>
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-2">
                <Label htmlFor="custom-route">Route</Label>
                <div className="flex gap-2">
                  <Input
                    id="custom-route"
                    value={customRoute}
                    onChange={(e) => setCustomRoute(e.target.value)}
                    placeholder="/example/route"
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    onClick={() => handleNavigate(customRoute)}
                    disabled={!customRoute}
                  >
                    Navigate
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={() => handleRefreshRoute(customRoute)}
                    disabled={!customRoute || isRefreshing}
                  >
                    Refresh
                  </Button>
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <h2 className="text-lg font-semibold mb-2">Custom Query Key</h2>
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-2">
                <Label htmlFor="custom-query-key">Query Key</Label>
                <div className="flex gap-2">
                  <Input
                    id="custom-query-key"
                    value={customQueryKey}
                    onChange={(e) => setCustomQueryKey(e.target.value)}
                    placeholder="exampleQueryKey"
                    className="flex-1"
                  />
                  <Button
                    variant="secondary"
                    onClick={() => handleRefreshQueryKey(customQueryKey)}
                    disabled={!customQueryKey || isRefreshing}
                  >
                    Refresh
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DataLoadingDebugPage;
