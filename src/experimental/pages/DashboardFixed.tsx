import React, { memo } from 'react';
import { PageTransition } from '@/components/layout/PageTransition';
import { ServiceProviderDashboard } from '@/components/service-providers/ServiceProviderDashboard';
import { useDashboardData } from '@/hooks/useDashboardDataFixed';
import { Container, Paper, Typography, Grid, Box, Divider, Button } from '@mui/material';
import { RecentDamageReports } from '@/components/dashboard/RecentDamageReports';
import { StatisticsCard } from '@/components/dashboard/StatisticsCard';
import { PropertyCard } from '@/components/properties/PropertyCard';
import { TaskCard } from '@/components/maintenance/TaskCard';
import { RecentTaskList } from '@/components/dashboard/RecentTaskList';
import { PropertyCounts } from '@/components/dashboard/PropertyCounts';
import { useAuth } from '@/contexts/AuthContext';
import { MaintenanceCounts } from '@/components/dashboard/MaintenanceCounts';
import { PurchaseOrderCounts } from '@/components/dashboard/PurchaseOrderCounts';
import { RecentPurchaseOrders } from '@/components/dashboard/RecentPurchaseOrders';
import { InventoryCounts } from '@/components/dashboard/InventoryCounts';
import { DashboardSkeleton } from '@/components/dashboard/DashboardSkeleton';
import { usePermissions } from '@/hooks/usePermissionsFixed';
import HomeIcon from '@mui/icons-material/Home';
import BuildIcon from '@mui/icons-material/Build';
import ReceiptIcon from '@mui/icons-material/Receipt';
import WarehouseIcon from '@mui/icons-material/Warehouse';
import HandymanIcon from '@mui/icons-material/Handyman';
import RefreshIcon from '@mui/icons-material/Refresh';
import { PropertyAddButton } from '@/components/properties/PropertyAddButton';
import { PermissionGuard } from '@/components/common/PermissionGuard';
import { PermissionType } from '@/types/auth';
import { Link } from 'react-router-dom';
import { useTeam } from '@/hooks/useTeam';

// Create the content component that uses hooks
const DashboardContent = () => {
  const { authState } = useAuth();
  const { hasPermission, isAdmin } = usePermissions();
  const { activeTeam } = useTeam();
  const userId = authState.user?.id;
  
  const dashboardData = useDashboardData(false);
  
  const {
    loading,
    properties,
    propertyStats,
    maintenanceStats,
    damageReports,
    maintenanceTasks,
    purchaseOrders,
    purchaseOrderStats,
    inventoryStats,
    refetch,
    isRefreshing
  } = dashboardData;

  // For service providers, show a simpler UI
  if (authState?.profile?.role === 'service_provider') {
    return <ServiceProviderDashboard />;
  }

  if (loading && properties.length === 0) {
    return <DashboardSkeleton />;
  }

  // Calculate the number of items to show based on available screen space
  // and what data is available
  const showPropertyCount = propertyStats?.total;
  const propertiesToShow = Math.min(properties.length, 3);
  const tasksToShow = Math.min(maintenanceTasks?.length || 0, propertiesToShow > 0 ? 2 : 3);

  return (
    <Container maxWidth="xl" sx={{ mt: 3, mb: 6 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Dashboard
        </Typography>
        <Button
          startIcon={<RefreshIcon />}
          onClick={() => refetch()}
          disabled={isRefreshing}
        >
          Refresh
        </Button>
      </Box>

      {/* Statistics Section */}
      <Grid container spacing={3} mb={4}>
        {showPropertyCount && (
          <Grid item xs={12} sm={6} md={3}>
            <StatisticsCard
              icon={<HomeIcon fontSize="large" />}
              title="Properties"
              value={propertyStats?.total || 0}
              subtitle={`${propertyStats?.occupied || 0} occupied, ${propertyStats?.vacant || 0} vacant`}
              footerText={`${propertyStats?.occupancyRate || 0}% occupancy rate`}
              colorVariant="primary"
              to="/properties"
            />
          </Grid>
        )}

        <Grid item xs={12} sm={6} md={3}>
          <StatisticsCard
            icon={<BuildIcon fontSize="large" />}
            title="Maintenance"
            value={maintenanceStats?.total || 0}
            subtitle={`${maintenanceStats?.pending || 0} pending, ${maintenanceStats?.inProgress || 0} in progress`}
            footerText={`${maintenanceStats?.completed || 0} completed`}
            colorVariant="info"
            to="/maintenance"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatisticsCard
            icon={<ReceiptIcon fontSize="large" />}
            title="Purchase Orders"
            value={purchaseOrderStats?.total || 0}
            subtitle={`${purchaseOrderStats?.pending || 0} pending, ${purchaseOrderStats?.ordered || 0} ordered`}
            footerText={`${purchaseOrderStats?.delivered || 0} delivered`}
            colorVariant="warning"
            to="/purchase-orders"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatisticsCard
            icon={<WarehouseIcon fontSize="large" />}
            title="Inventory"
            value={inventoryStats?.total || 0}
            subtitle={`${inventoryStats?.lowStock || 0} low stock, ${inventoryStats?.outOfStock || 0} out of stock`}
            footerText={`$${inventoryStats?.value?.toFixed(2) || 0} total value`}
            colorVariant="success"
            to="/inventory"
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* First Column */}
        <Grid item xs={12} md={6} lg={4}>
          <Box mb={3}>
            <Paper elevation={2} sx={{ p: 2 }}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6" fontWeight="bold">Properties</Typography>
                <PermissionGuard permission={PermissionType.MANAGE_PROPERTIES} teamId={activeTeam?.id}>
                  <PropertyAddButton />
                </PermissionGuard>
              </Box>
              
              {propertyStats && (
                <PropertyCounts 
                  total={propertyStats.total} 
                  occupied={propertyStats.occupied} 
                  vacant={propertyStats.vacant} 
                />
              )}
              
              <Box mt={2}>
                {properties.length > 0 ? (
                  properties.slice(0, propertiesToShow).map(property => (
                    <PropertyCard 
                      key={property.id} 
                      property={property}
                      displayMode="compact"
                    />
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary" align="center" my={2}>
                    No properties found
                  </Typography>
                )}
                
                {properties.length > 0 && (
                  <Box mt={2} display="flex" justifyContent="center">
                    <Button 
                      component={Link} 
                      to="/properties"
                      variant="outlined" 
                      color="primary"
                      size="small"
                    >
                      View All Properties
                    </Button>
                  </Box>
                )}
              </Box>
            </Paper>
          </Box>

          <Paper elevation={2} sx={{ p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6" fontWeight="bold">Maintenance</Typography>
              <Button 
                component={Link} 
                to="/maintenance"
                size="small" 
                endIcon={<HandymanIcon />}
              >
                View All
              </Button>
            </Box>
            
            <MaintenanceCounts 
              pending={maintenanceStats?.pending || 0}
              inProgress={maintenanceStats?.inProgress || 0}
              completed={maintenanceStats?.completed || 0}
            />
            
            <Divider sx={{ my: 2 }} />
            
            <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
              Recent Tasks
            </Typography>
            
            {maintenanceTasks && maintenanceTasks.length > 0 ? (
              <Box mt={1}>
                {maintenanceTasks.slice(0, tasksToShow).map(task => (
                  <TaskCard 
                    key={task.id} 
                    task={task}
                    displayMode="compact"
                  />
                ))}
              </Box>
            ) : (
              <Typography variant="body2" color="text.secondary" align="center" my={2}>
                No maintenance tasks found
              </Typography>
            )}
            
            {maintenanceStats?.recentlyCompleted?.length > 0 && (
              <Box mt={2}>
                <Typography variant="subtitle2" fontWeight="medium" gutterBottom>
                  Recently Completed
                </Typography>
                <RecentTaskList tasks={maintenanceStats.recentlyCompleted} />
              </Box>
            )}
          </Paper>
        </Grid>
        
        {/* Second Column */}
        <Grid item xs={12} md={6} lg={4}>
          <Box mb={3}>
            <Paper elevation={2} sx={{ p: 2 }}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6" fontWeight="bold">Damage Reports</Typography>
                <Button 
                  component={Link} 
                  to="/damage-reports"
                  size="small"
                >
                  View All
                </Button>
              </Box>
              
              <RecentDamageReports reports={damageReports} />
            </Paper>
          </Box>
          
          <Paper elevation={2} sx={{ p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6" fontWeight="bold">Purchase Orders</Typography>
              <Button 
                component={Link} 
                to="/purchase-orders"
                size="small"
              >
                View All
              </Button>
            </Box>
            
            <PurchaseOrderCounts 
              pending={purchaseOrderStats?.pending || 0}
              ordered={purchaseOrderStats?.ordered || 0}
              delivered={purchaseOrderStats?.delivered || 0}
            />
            
            <Divider sx={{ my: 2 }} />
            
            <RecentPurchaseOrders orders={purchaseOrderStats?.recent || []} />
          </Paper>
        </Grid>
        
        {/* Third Column */}
        <Grid item xs={12} lg={4}>
          <Paper elevation={2} sx={{ p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6" fontWeight="bold">Inventory</Typography>
              <Button 
                component={Link} 
                to="/inventory"
                size="small"
              >
                View All
              </Button>
            </Box>
            
            <InventoryCounts 
              total={inventoryStats?.total || 0}
              lowStock={inventoryStats?.lowStock || 0}
              outOfStock={inventoryStats?.outOfStock || 0}
              value={inventoryStats?.value || 0}
            />
            
            <Divider sx={{ my: 2 }} />
            
            <Typography variant="subtitle2" gutterBottom>
              Low Stock Items
            </Typography>
            
            {/* TODO: Add list of low stock items once inventory API is ready */}
            <Typography variant="body2" color="text.secondary" align="center" my={2}>
              Inventory details coming soon
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

// Main component - A simple wrapper that doesn't use hooks directly
const Dashboard = memo(() => {
  return (
    <PageTransition>
      <DashboardContent />
    </PageTransition>
  );
});

Dashboard.displayName = 'Dashboard';

export default Dashboard;
