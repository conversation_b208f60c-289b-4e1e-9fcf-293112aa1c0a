import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import DataLoadingDebugPage from './DataLoadingDebugPage';

// Mock react-query hooks and functions
jest.mock('@tanstack/react-query', () => ({
  useQueryClient: () => ({
    invalidateQueries: jest.fn(),
    refetchQueries: jest.fn(),
  }),
}));

jest.mock('@/hooks/useNavigationRefresh', () => ({
  useNavigationRefresh: () => ({
    refreshRouteData: jest.fn(),
  }),
}));

// Mock the DataLoadingDebugger and DataLoadingTestComponent as they are external
jest.mock('@/components/debug/DataLoadingDebugger', () => () => <div data-testid="data-loading-debugger" />);
jest.mock('@/components/debug/DataLoadingTestComponent', () => () => <div data-testid="data-loading-test-component" />);

describe('DataLoadingDebugPage', () => {
  test('renders the debug page title', () => {
    render(
      <MemoryRouter>
        <DataLoadingDebugPage />
      </MemoryRouter>
    );

    expect(screen.getByText(/Data Loading Debug Page/i)).toBeInTheDocument();
    expect(screen.getByTestId('data-loading-debugger')).toBeInTheDocument();
    expect(screen.getByTestId('data-loading-test-component')).toBeInTheDocument();
  });
});
