import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import { AuthContext } from '@/contexts/AuthContext';
import DashboardFixed from './DashboardFixed';

// Mock the useDashboardData hook
jest.mock('@/hooks/useDashboardDataFixed', () => ({
  useDashboardData: () => ({
    loading: true,
    properties: [],
    propertyStats: null,
    maintenanceStats: null,
    damageReports: [],
    maintenanceTasks: [],
    purchaseOrders: [],
    purchaseOrderStats: null,
    inventoryStats: null,
    refetch: jest.fn(),
    isRefreshing: false,
  }),
}));

// Mock the useAuth hook
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    authState: { user: { id: '123' }, profile: { role: 'property_manager' } },
  }),
}));

// Mock the usePermissions hook
jest.mock('@/hooks/usePermissionsFixed', () => ({
  usePermissions: () => ({
    hasPermission: jest.fn().mockReturnValue(true),
    isAdmin: jest.fn().mockReturnValue(false),
  }),
}));

// Mock the useTeam hook
jest.mock('@/hooks/useTeam', () => ({
  useTeam: () => ({
    activeTeam: { id: 'team1' },
  }),
}));

describe('DashboardFixed', () => {
  test('renders the dashboard with loading state', () => {
    render(
      <MemoryRouter>
        <AuthContext.Provider value={{ authState: { user: { id: '123' }, profile: { role: 'property_manager' } } }}>
          <DashboardFixed />
        </AuthContext.Provider>
      </MemoryRouter>
    );

    expect(screen.getByText(/Dashboard/i)).toBeInTheDocument();
    expect(screen.getByTestId('dashboard-skeleton')).toBeInTheDocument(); // Assuming DashboardSkeleton has data-testid="dashboard-skeleton"
  });
});
