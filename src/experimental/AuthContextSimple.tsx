import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabaseSimple } from '@/integrations/supabase/client-simple'

interface AuthState {
  user: User | null
  session: Session | null
  isAuthenticated: boolean
  isLoading: boolean
}

interface AuthContextType {
  authState: AuthState
  signIn: (email: string, password: string) => Promise<{ error?: any }>
  signOut: () => Promise<void>
  refreshSession: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const AuthProviderSimple: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    isAuthenticated: false,
    isLoading: true
  })

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabaseSimple.auth.getSession()
        
        if (error) {
          console.error('Error getting session:', error)
        }

        setAuthState({
          user: session?.user ?? null,
          session: session,
          isAuthenticated: !!session?.user,
          isLoading: false
        })
      } catch (error) {
        console.error('Error in getInitialSession:', error)
        setAuthState({
          user: null,
          session: null,
          isAuthenticated: false,
          isLoading: false
        })
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabaseSimple.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id)
        
        setAuthState({
          user: session?.user ?? null,
          session: session,
          isAuthenticated: !!session?.user,
          isLoading: false
        })
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabaseSimple.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        return { error }
      }

      return { data }
    } catch (error) {
      return { error }
    }
  }

  const signOut = async () => {
    try {
      await supabaseSimple.auth.signOut()
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const refreshSession = async () => {
    try {
      const { data: { session }, error } = await supabaseSimple.auth.refreshSession()
      
      if (error) {
        console.error('Error refreshing session:', error)
        return
      }

      setAuthState(prev => ({
        ...prev,
        user: session?.user ?? null,
        session: session,
        isAuthenticated: !!session?.user
      }))
    } catch (error) {
      console.error('Error in refreshSession:', error)
    }
  }

  return (
    <AuthContext.Provider value={{
      authState,
      signIn,
      signOut,
      refreshSession
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuthSimple = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuthSimple must be used within an AuthProviderSimple')
  }
  return context
}
