import React from 'react'
import { AuthProviderSimple, useAuthSimple } from '@/contexts/AuthContextSimple'
import { usePropertiesSimple } from '@/hooks/usePropertiesSimple'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

const TestContent: React.FC = () => {
  const { authState, signOut } = useAuthSimple()
  const { properties, loading, error, refetch } = usePropertiesSimple()

  if (authState.isLoading) {
    return <div className="p-8">Loading auth...</div>
  }

  if (!authState.isAuthenticated) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Not Authenticated</h1>
        <p>Please log in to test the simple integration.</p>
      </div>
    )
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Simple Supabase Integration Test</h1>
        <p className="text-gray-600">Testing clean, minimal Supabase client</p>
      </div>

      {/* Auth Status */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Authentication Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p><strong>User ID:</strong> {authState.user?.id}</p>
            <p><strong>Email:</strong> {authState.user?.email}</p>
            <p><strong>Authenticated:</strong> {authState.isAuthenticated ? '✅ Yes' : '❌ No'}</p>
            <Button onClick={signOut} variant="outline" className="mt-2">
              Sign Out
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Properties */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Properties
            <Button onClick={refetch} variant="outline" size="sm">
              Refresh
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading && <p>Loading properties...</p>}
          
          {error && (
            <div className="bg-red-50 border border-red-200 rounded p-4 mb-4">
              <p className="text-red-800"><strong>Error:</strong> {error}</p>
            </div>
          )}

          {!loading && !error && (
            <div>
              <p className="mb-4"><strong>Found {properties.length} properties</strong></p>
              
              {properties.length === 0 ? (
                <p className="text-gray-500">No properties found.</p>
              ) : (
                <div className="space-y-2">
                  {properties.map((property) => (
                    <div key={property.id} className="border rounded p-3">
                      <h3 className="font-semibold">{property.name}</h3>
                      <p className="text-sm text-gray-600">
                        {property.address}, {property.city}, {property.state} {property.zip}
                      </p>
                      <p className="text-xs text-gray-500">
                        {property.bedrooms} bed, {property.bathrooms} bath
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Network Test */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Network Test</CardTitle>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={async () => {
              try {
                console.log('🧪 Testing direct Supabase connection...')
                const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
                const supabaseKey = import.meta.env.VITE_SUPABASE_PUBLISHABLE_KEY;
                
                if (!supabaseUrl || !supabaseKey) {
                  throw new Error('Missing Supabase environment variables');
                }
                
                const response = await fetch(`${supabaseUrl}/rest/v1/`, {
                  headers: {
                    'apikey': supabaseKey
                  }
                })
                
                if (response.ok) {
                  console.log('✅ Network test successful')
                  alert('✅ Network test successful!')
                } else {
                  console.log('❌ Network test failed')
                  alert('❌ Network test failed')
                }
              } catch (err) {
                console.error('❌ Network test error:', err)
                alert('❌ Network test error: ' + err)
              }
            }}
            variant="outline"
          >
            Test Network Connection
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

const TestSimple: React.FC = () => {
  return (
    <AuthProviderSimple>
      <TestContent />
    </AuthProviderSimple>
  )
}

export default TestSimple
