import { useState, useCallback } from 'react';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export interface AppearanceSettings {
  theme: 'light' | 'dark' | 'system';
  primaryColor: string;
  accentColor: string;
  fontFamily: string;
  fontSize: 'small' | 'medium' | 'large';
  borderRadius: 'none' | 'small' | 'medium' | 'large';
  animations: boolean;
}

export interface AppearanceSettingsData {
  settings: AppearanceSettings;
  loading: boolean;
  error: string | null;
  isError: boolean;
  fetchSettings: () => Promise<void>;
  updateSettings: (settings: Partial<AppearanceSettings>) => Promise<boolean>;
  resetSettings: () => Promise<boolean>;
}

const DEFAULT_SETTINGS: AppearanceSettings = {
  theme: 'light',
  primaryColor: '#1976d2',
  accentColor: '#f50057',
  fontFamily: 'Inter, system-ui, sans-serif',
  fontSize: 'medium',
  borderRadius: 'medium',
  animations: true
};

/**
 * A standardized hook for fetching and updating appearance settings using React Query
 * This follows the same pattern as useOperationsDataQuery for consistency
 */
export const useAppearanceSettingsQueryV2 = (): AppearanceSettingsData => {
  const { authState } = useAuth();
  const userId = authState?.user?.id;
  const queryClient = useQueryClient();
  const [retryCount, setRetryCount] = useState(0);

  // Function to retry data fetching manually
  const retryFetch = useCallback(async () => {
    console.log('[useAppearanceSettingsQueryV2] Manual refresh triggered');
    setRetryCount(prev => prev + 1);
    await queryClient.invalidateQueries({ queryKey: ['appearanceSettingsV2'] });
    await queryClient.refetchQueries({ queryKey: ['appearanceSettingsV2'] });
  }, [queryClient]);

  // Fetch appearance settings
  const { 
    data: settings = DEFAULT_SETTINGS,
    isLoading,
    error,
    isError
  } = useQuery({
    queryKey: ['appearanceSettingsV2'],
    queryFn: async () => {
      try {
        if (!userId) {
          throw new Error('User not authenticated');
        }

        console.log(`[useAppearanceSettingsQueryV2] Fetching appearance settings (attempt ${retryCount + 1})`);

        const { data, error } = await supabase
          .from('user_settings')
          .select('appearance_settings')
          .eq('user_id', userId)
          .maybeSingle();

        if (error) {
          console.error('[useAppearanceSettingsQueryV2] Error fetching appearance settings:', error);
          throw error;
        }

        if (!data || !data.appearance_settings) {
          console.log('[useAppearanceSettingsQueryV2] No appearance settings found, using defaults');
          return DEFAULT_SETTINGS;
        }

        console.log('[useAppearanceSettingsQueryV2] Successfully loaded appearance settings');
        return {
          ...DEFAULT_SETTINGS,
          ...data.appearance_settings
        };
      } catch (err: any) {
        console.error('[useAppearanceSettingsQueryV2] Error fetching appearance settings:', err);
        throw err;
      }
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: true,
    refetchOnReconnect: true,
    enabled: !!userId,
    networkMode: 'always'
  });

  // Update settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: async (newSettings: Partial<AppearanceSettings>) => {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log('[useAppearanceSettingsQueryV2] Updating appearance settings:', newSettings);

      // Check if settings already exist
      const { data: existingData, error: checkError } = await supabase
        .from('user_settings')
        .select('id, appearance_settings')
        .eq('user_id', userId)
        .maybeSingle();

      if (checkError) {
        console.error('[useAppearanceSettingsQueryV2] Error checking existing settings:', checkError);
        throw checkError;
      }

      const updatedSettings = {
        ...DEFAULT_SETTINGS,
        ...(existingData?.appearance_settings || {}),
        ...newSettings
      };

      if (existingData) {
        // Update existing settings
        const { error } = await supabase
          .from('user_settings')
          .update({
            appearance_settings: updatedSettings,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingData.id);

        if (error) {
          console.error('[useAppearanceSettingsQueryV2] Error updating appearance settings:', error);
          throw error;
        }
      } else {
        // Create new settings
        const { error } = await supabase
          .from('user_settings')
          .insert({
            user_id: userId,
            appearance_settings: updatedSettings
          });

        if (error) {
          console.error('[useAppearanceSettingsQueryV2] Error creating appearance settings:', error);
          throw error;
        }
      }

      return updatedSettings;
    },
    onSuccess: (data) => {
      queryClient.setQueryData(['appearanceSettingsV2'], data);
    }
  });

  // Reset settings mutation
  const resetSettingsMutation = useMutation({
    mutationFn: async () => {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log('[useAppearanceSettingsQueryV2] Resetting appearance settings to defaults');

      const { data: existingData, error: checkError } = await supabase
        .from('user_settings')
        .select('id')
        .eq('user_id', userId)
        .maybeSingle();

      if (checkError) {
        console.error('[useAppearanceSettingsQueryV2] Error checking existing settings:', checkError);
        throw checkError;
      }

      if (existingData) {
        // Update existing settings to defaults
        const { error } = await supabase
          .from('user_settings')
          .update({
            appearance_settings: DEFAULT_SETTINGS,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingData.id);

        if (error) {
          console.error('[useAppearanceSettingsQueryV2] Error resetting appearance settings:', error);
          throw error;
        }
      }

      return DEFAULT_SETTINGS;
    },
    onSuccess: (data) => {
      queryClient.setQueryData(['appearanceSettingsV2'], data);
    }
  });

  // Wrapper functions
  const updateSettings = async (newSettings: Partial<AppearanceSettings>): Promise<boolean> => {
    try {
      await updateSettingsMutation.mutateAsync(newSettings);
      return true;
    } catch (error) {
      return false;
    }
  };

  const resetSettings = async (): Promise<boolean> => {
    try {
      await resetSettingsMutation.mutateAsync();
      return true;
    } catch (error) {
      return false;
    }
  };

  return {
    settings,
    loading: isLoading,
    error: error ? String(error) : null,
    isError,
    fetchSettings: retryFetch,
    updateSettings,
    resetSettings
  };
};
