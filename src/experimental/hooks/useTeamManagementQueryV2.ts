import { useState, useCallback } from 'react';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export interface Team {
  id: string;
  name: string;
  owner_id: string;
  created_at: string;
  updated_at: string;
  member_count?: number;
}

export interface TeamMember {
  id: string;
  team_id: string;
  user_id: string;
  added_by: string;
  status: string;
  created_at: string;
  updated_at: string;
  user_email?: string;
  user_name?: string;
  role?: string;
}

export interface TeamInvitation {
  id: string;
  team_id: string;
  email: string;
  status: string;
  created_at: string;
  updated_at: string;
  team_name?: string;
}

export interface TeamManagementData {
  teams: Team[];
  teamMembers: TeamMember[];
  sentInvitations: TeamInvitation[];
  loading: boolean;
  loadingTeamMembers: boolean;
  loadingInvitations: boolean;
  error: string | null;
  isError: boolean;
  fetchTeams: () => Promise<void>;
  fetchTeamMembers: (teamId: string) => Promise<void>;
  fetchSentInvitations: (teamId: string) => Promise<void>;
  createTeam: (name: string) => Promise<Team | null>;
  updateTeam: (teamId: string, name: string) => Promise<boolean>;
  deleteTeam: (teamId: string) => Promise<boolean>;
  inviteUserToTeam: (teamId: string, email: string, role?: string) => Promise<boolean>;
  removeTeamMember: (teamMemberId: string) => Promise<boolean>;
  deleteInvitation: (invitationId: string) => Promise<boolean>;
}

/**
 * A standardized hook for team management using React Query
 * This follows the same pattern as useOperationsDataQuery for consistency
 */
export const useTeamManagementQueryV2 = (): TeamManagementData => {
  const { authState } = useAuth();
  const userId = authState?.user?.id;
  const queryClient = useQueryClient();
  const [retryCount, setRetryCount] = useState(0);
  const [teamMembersRetryCount, setTeamMembersRetryCount] = useState(0);
  const [invitationsRetryCount, setInvitationsRetryCount] = useState(0);
  const [currentTeamId, setCurrentTeamId] = useState<string | null>(null);

  // Function to retry data fetching manually
  const retryFetch = useCallback(async () => {
    console.log('[useTeamManagementQueryV2] Manual refresh triggered');
    setRetryCount(prev => prev + 1);
    await queryClient.invalidateQueries({ queryKey: ['teamsV2'] });
    await queryClient.refetchQueries({ queryKey: ['teamsV2'] });
  }, [queryClient]);

  // Function to retry team members fetching manually
  const retryFetchTeamMembers = useCallback(async (teamId: string) => {
    console.log(`[useTeamManagementQueryV2] Manual team members refresh triggered for team ${teamId}`);

    // Set the current team ID first to ensure the query is enabled
    setCurrentTeamId(teamId);

    // Then increment the retry count to force a refetch
    setTeamMembersRetryCount(prev => prev + 1);

    // Invalidate and refetch with the correct query key
    await queryClient.invalidateQueries({
      queryKey: ['teamMembersV2', teamId],
      exact: false
    });

    await queryClient.refetchQueries({
      queryKey: ['teamMembersV2', teamId],
      exact: false,
      type: 'all'
    });

    // Also invalidate and refetch team permissions
    await queryClient.invalidateQueries({
      queryKey: ['teamPermissionsV2', teamId],
      exact: false
    });

    await queryClient.refetchQueries({
      queryKey: ['teamPermissionsV2', teamId],
      exact: false,
      type: 'all'
    });
  }, [queryClient]);

  // Function to retry invitations fetching manually
  const retryFetchInvitations = useCallback(async (teamId: string) => {
    console.log(`[useTeamManagementQueryV2] Manual invitations refresh triggered for team ${teamId}`);

    // Set the current team ID first to ensure the query is enabled
    setCurrentTeamId(teamId);

    // Then increment the retry count to force a refetch
    setInvitationsRetryCount(prev => prev + 1);

    // Invalidate and refetch with the correct query key
    await queryClient.invalidateQueries({
      queryKey: ['teamInvitationsV2', teamId],
      exact: false
    });

    await queryClient.refetchQueries({
      queryKey: ['teamInvitationsV2', teamId],
      exact: false,
      type: 'all'
    });
  }, [queryClient]);

  // Fetch teams
  const {
    data: teams = [],
    isLoading,
    error,
    isError
  } = useQuery({
    queryKey: ['teamsV2'],
    queryFn: async () => {
      try {
        if (!userId) {
          throw new Error('User not authenticated');
        }

        console.log(`[useTeamManagementQueryV2] Fetching teams (attempt ${retryCount + 1})`);

        // Use our RPC function to get teams
        const { data: rpcData, error: rpcError } = await supabase.rpc(
          'get_user_teams',
          { p_user_id: userId }
        );

        if (rpcError) {
          console.error('[useTeamManagementQueryV2] RPC function error:', rpcError);
          throw rpcError;
        }

        if (!rpcData || rpcData.length === 0) {
          console.log('[useTeamManagementQueryV2] No teams found');
          return [];
        }

        console.log(`[useTeamManagementQueryV2] Successfully loaded ${rpcData.length} teams`);
        return rpcData;
      } catch (err: any) {
        console.error('[useTeamManagementQueryV2] Error fetching teams:', err);
        throw err;
      }
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: true,
    refetchOnReconnect: true,
    enabled: !!userId,
    keepPreviousData: true, // Keep previous data while fetching new data to prevent flashing
    refetchInterval: false, // Disable automatic refetching
    networkMode: 'always'
  });

  // Fetch team members
  const {
    data: teamMembers = [],
    isLoading: isLoadingTeamMembers,
    error: teamMembersError
  } = useQuery({
    queryKey: ['teamMembersV2', currentTeamId],
    queryFn: async () => {
      try {
        if (!userId || !currentTeamId) {
          console.log('[useTeamManagementQueryV2] No userId or currentTeamId, returning empty array');
          return [];
        }

        console.log(`[useTeamManagementQueryV2] Fetching team members for team ${currentTeamId} (attempt ${teamMembersRetryCount + 1})`);

        // First try using the RPC function for better performance and reliability
        try {
          console.log(`[useTeamManagementQueryV2] Trying RPC function get_team_members for team ${currentTeamId}`);
          const { data: rpcData, error: rpcError } = await supabase.rpc(
            'get_team_members',
            { p_team_id: currentTeamId }
          );

          if (rpcError) {
            console.error('[useTeamManagementQueryV2] RPC function error:', rpcError);
            // Fall back to direct query
          } else if (rpcData && rpcData.length > 0) {
            console.log(`[useTeamManagementQueryV2] RPC function found ${rpcData.length} team members`);
            return rpcData;
          }
        } catch (rpcErr) {
          console.error('[useTeamManagementQueryV2] Error calling RPC function:', rpcErr);
          // Fall back to direct query
        }

        // Fall back to direct query
        console.log(`[useTeamManagementQueryV2] Falling back to direct query for team ${currentTeamId}`);

        // Use a more reliable query with explicit joins
        const { data, error } = await supabase
          .from('team_members')
          .select(`
            id,
            team_id,
            user_id,
            added_by,
            status,
            created_at,
            updated_at,
            profiles:user_id (id, email, first_name, last_name, role)
          `)
          .eq('team_id', currentTeamId);

        if (error) {
          console.error('[useTeamManagementQueryV2] Error fetching team members:', error);
          throw error;
        }

        console.log(`[useTeamManagementQueryV2] Successfully loaded ${data?.length || 0} team members`);

        // Format team members
        return (data || []).map(member => ({
          id: member.id,
          team_id: member.team_id,
          user_id: member.user_id,
          added_by: member.added_by,
          status: member.status,
          created_at: member.created_at,
          updated_at: member.updated_at,
          user_email: member.profiles?.email || '',
          user_name: `${member.profiles?.first_name || ''} ${member.profiles?.last_name || ''}`.trim() || 'Unknown User',
          role: member.profiles?.role || 'user'
        }));
      } catch (err: any) {
        console.error('[useTeamManagementQueryV2] Error fetching team members:', err);
        throw err;
      }
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: true,
    refetchOnReconnect: true,
    enabled: !!userId && !!currentTeamId,
    keepPreviousData: true, // Keep previous data while fetching new data to prevent flashing
    refetchInterval: false, // Disable automatic refetching
    networkMode: 'always'
  });

  // Fetch sent invitations
  const {
    data: sentInvitations = [],
    isLoading: isLoadingInvitations,
    error: invitationsError
  } = useQuery({
    queryKey: ['teamInvitationsV2', currentTeamId],
    queryFn: async () => {
      try {
        if (!userId || !currentTeamId) {
          return [];
        }

        console.log(`[useTeamManagementQueryV2] Fetching invitations for team ${currentTeamId} (attempt ${invitationsRetryCount + 1})`);

        const { data, error } = await supabase
          .from('team_invitations')
          .select(`
            *,
            teams:team_id (name)
          `)
          .eq('team_id', currentTeamId)
          .eq('status', 'pending');

        if (error) {
          console.error('[useTeamManagementQueryV2] Error fetching invitations:', error);
          throw error;
        }

        console.log(`[useTeamManagementQueryV2] Successfully loaded ${data?.length || 0} invitations`);

        // Format invitations
        return (data || []).map(invitation => ({
          id: invitation.id,
          team_id: invitation.team_id,
          email: invitation.email,
          status: invitation.status,
          created_at: invitation.created_at,
          updated_at: invitation.updated_at,
          team_name: invitation.teams?.name || ''
        }));
      } catch (err: any) {
        console.error('[useTeamManagementQueryV2] Error fetching invitations:', err);
        throw err;
      }
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: true,
    refetchOnReconnect: true,
    enabled: !!userId && !!currentTeamId,
    keepPreviousData: true, // Keep previous data while fetching new data to prevent flashing
    refetchInterval: false, // Disable automatic refetching
    networkMode: 'always'
  });

  // Create team mutation
  const createTeamMutation = useMutation({
    mutationFn: async (name: string) => {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log('[useTeamManagementQueryV2] Creating new team:', name);

      // Create the team
      const { data, error } = await supabase
        .from('teams')
        .insert({
          name,
          owner_id: userId
        })
        .select()
        .single();

      if (error) {
        console.error('[useTeamManagementQueryV2] Error creating team:', error);
        throw error;
      }

      // Add the owner to the team_members table
      const { error: memberError } = await supabase
        .from('team_members')
        .insert({
          team_id: data.id,
          user_id: userId,
          added_by: userId,
          status: 'active'
        });

      if (memberError) {
        console.error('[useTeamManagementQueryV2] Error adding owner to team:', memberError);
        throw memberError;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teamsV2'] });
    }
  });

  // Update team mutation
  const updateTeamMutation = useMutation({
    mutationFn: async ({ teamId, name }: { teamId: string, name: string }) => {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log(`[useTeamManagementQueryV2] Updating team ${teamId} to name: ${name}`);

      const { error } = await supabase
        .from('teams')
        .update({ name })
        .eq('id', teamId)
        .eq('owner_id', userId);

      if (error) {
        console.error('[useTeamManagementQueryV2] Error updating team:', error);
        throw error;
      }

      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teamsV2'] });
    }
  });

  // Delete team mutation
  const deleteTeamMutation = useMutation({
    mutationFn: async (teamId: string) => {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log(`[useTeamManagementQueryV2] Deleting team ${teamId}`);

      const { error } = await supabase
        .from('teams')
        .delete()
        .eq('id', teamId)
        .eq('owner_id', userId);

      if (error) {
        console.error('[useTeamManagementQueryV2] Error deleting team:', error);
        throw error;
      }

      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teamsV2'] });
    }
  });

  // Invite user to team mutation
  const inviteUserMutation = useMutation({
    mutationFn: async ({ teamId, email, role }: { teamId: string, email: string, role?: string }) => {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log(`[useTeamManagementQueryV2] Inviting ${email} to team ${teamId}`);

      // Check if invitation already exists
      const { data: existingInvitation, error: checkError } = await supabase
        .from('team_invitations')
        .select('id')
        .eq('team_id', teamId)
        .eq('email', email)
        .eq('status', 'pending')
        .maybeSingle();

      if (checkError) {
        console.error('[useTeamManagementQueryV2] Error checking existing invitation:', checkError);
        throw checkError;
      }

      if (existingInvitation) {
        console.log('[useTeamManagementQueryV2] Invitation already exists');
        return true;
      }

      // Create the invitation
      const { error } = await supabase
        .from('team_invitations')
        .insert({
          team_id: teamId,
          email,
          invited_by: userId,
          status: 'pending',
          role: role || 'user'
        });

      if (error) {
        console.error('[useTeamManagementQueryV2] Error creating invitation:', error);
        throw error;
      }

      return true;
    },
    onSuccess: () => {
      if (currentTeamId) {
        queryClient.invalidateQueries({ queryKey: ['teamInvitationsV2', currentTeamId] });
      }
    }
  });

  // Remove team member mutation
  const removeTeamMemberMutation = useMutation({
    mutationFn: async (teamMemberId: string) => {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log(`[useTeamManagementQueryV2] Removing team member ${teamMemberId}`);

      const { error } = await supabase
        .from('team_members')
        .delete()
        .eq('id', teamMemberId);

      if (error) {
        console.error('[useTeamManagementQueryV2] Error removing team member:', error);
        throw error;
      }

      return true;
    },
    onSuccess: () => {
      if (currentTeamId) {
        queryClient.invalidateQueries({ queryKey: ['teamMembersV2', currentTeamId] });
      }
    }
  });

  // Delete invitation mutation
  const deleteInvitationMutation = useMutation({
    mutationFn: async (invitationId: string) => {
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log(`[useTeamManagementQueryV2] Deleting invitation ${invitationId}`);

      const { error } = await supabase
        .from('team_invitations')
        .delete()
        .eq('id', invitationId);

      if (error) {
        console.error('[useTeamManagementQueryV2] Error deleting invitation:', error);
        throw error;
      }

      return true;
    },
    onSuccess: () => {
      if (currentTeamId) {
        queryClient.invalidateQueries({ queryKey: ['teamInvitationsV2', currentTeamId] });
      }
    }
  });

  // Wrapper functions
  const fetchTeams = retryFetch;

  const fetchTeamMembers = async (teamId: string) => {
    setCurrentTeamId(teamId);
    await retryFetchTeamMembers(teamId);
  };

  const fetchSentInvitations = async (teamId: string) => {
    setCurrentTeamId(teamId);
    await retryFetchInvitations(teamId);
  };

  const createTeam = async (name: string): Promise<Team | null> => {
    try {
      return await createTeamMutation.mutateAsync(name);
    } catch (error) {
      return null;
    }
  };

  const updateTeam = async (teamId: string, name: string): Promise<boolean> => {
    try {
      await updateTeamMutation.mutateAsync({ teamId, name });
      return true;
    } catch (error) {
      return false;
    }
  };

  const deleteTeam = async (teamId: string): Promise<boolean> => {
    try {
      await deleteTeamMutation.mutateAsync(teamId);
      return true;
    } catch (error) {
      return false;
    }
  };

  const inviteUserToTeam = async (teamId: string, email: string, role?: string): Promise<boolean> => {
    try {
      await inviteUserMutation.mutateAsync({ teamId, email, role });
      return true;
    } catch (error) {
      return false;
    }
  };

  const removeTeamMember = async (teamMemberId: string): Promise<boolean> => {
    try {
      await removeTeamMemberMutation.mutateAsync(teamMemberId);
      return true;
    } catch (error) {
      return false;
    }
  };

  const deleteInvitation = async (invitationId: string): Promise<boolean> => {
    try {
      await deleteInvitationMutation.mutateAsync(invitationId);
      return true;
    } catch (error) {
      return false;
    }
  };

  // Add error retry effect similar to other hooks
  useEffect(() => {
    // Only retry on actual errors, not on empty data
    if (isError) {
      const timer = setTimeout(() => {
        if (retryCount < 3) {
          console.log('[useTeamManagementQueryV2] Auto-retrying teams data fetch due to error');
          retryFetch();
        } else {
          console.error('[useTeamManagementQueryV2] Failed to load teams after multiple attempts');
        }
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [isError, retryCount, retryFetch]);

  // Add error retry effect for team members
  useEffect(() => {
    // Only retry if we have a current team ID and there's an error
    if (teamMembersError && currentTeamId) {
      const timer = setTimeout(() => {
        if (teamMembersRetryCount < 3) {
          console.log(`[useTeamManagementQueryV2] Auto-retrying team members data fetch for team ${currentTeamId} due to error`);
          retryFetchTeamMembers(currentTeamId);
        } else {
          console.error(`[useTeamManagementQueryV2] Failed to load team members for team ${currentTeamId} after multiple attempts`);
        }
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [teamMembersError, teamMembersRetryCount, currentTeamId, retryFetchTeamMembers]);

  // Add error retry effect for invitations
  useEffect(() => {
    // Only retry if we have a current team ID and there's an error
    if (invitationsError && currentTeamId) {
      const timer = setTimeout(() => {
        if (invitationsRetryCount < 3) {
          console.log(`[useTeamManagementQueryV2] Auto-retrying invitations data fetch for team ${currentTeamId} due to error`);
          retryFetchInvitations(currentTeamId);
        } else {
          console.error(`[useTeamManagementQueryV2] Failed to load invitations for team ${currentTeamId} after multiple attempts`);
        }
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [invitationsError, invitationsRetryCount, currentTeamId, retryFetchInvitations]);

  return {
    teams,
    teamMembers,
    sentInvitations,
    loading: isLoading,
    loadingTeamMembers: isLoadingTeamMembers,
    loadingInvitations: isLoadingInvitations,
    error: error ? String(error) : null,
    isError,
    fetchTeams,
    fetchTeamMembers,
    fetchSentInvitations,
    createTeam,
    updateTeam,
    deleteTeam,
    inviteUserToTeam,
    removeTeamMember,
    deleteInvitation
  };
};
