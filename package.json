{"name": "stayfu", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "dev:local": "vite --mode development", "dev:remote": "vite --mode production", "build": "NODE_OPTIONS=\"--max-old-space-size=4096\" vite build --mode production", "build:vercel": "NODE_ENV=production vite build --mode production", "build:dev": "vite build --mode development", "lint": "eslint . --config config/eslint.config.js", "preview": "vite preview", "test": "npx jest --config=config/jest.config.js --testPathPattern=src/working.test.js", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:generate": "node scripts/generate-test.js", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:status": "supabase status", "supabase:types": "supabase gen types typescript --project-id pwaeknalhosfwuxkpaet --schema public > src/lib/supabase-types.ts", "supabase:types:local": "supabase gen types typescript --local > src/lib/supabase-types.ts"}, "dependencies": {"@google/generative-ai": "^0.21.0", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.56.2", "@testing-library/dom": "^10.4.0", "@tinymce/tinymce-react": "^5.1.1", "caniuse-lite": "^1.0.30001707", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cypress": "^13.6.6", "date-fns": "^4.1.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.4.10", "heic2any": "^0.0.4", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "ical": "^0.8.0", "input-otp": "^1.2.4", "jest": "^29.7.0", "jspdf": "^3.0.1", "jszip": "^3.10.1", "lucide-react": "^0.462.0", "msw": "^2.7.3", "next-themes": "^0.3.0", "puppeteer": "^24.15.0", "react": "^18.3.1", "react-day-picker": "^9.6.7", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "react-to-print": "^3.1.0", "recharts": "^2.12.7", "sonner": "^1.5.0", "supabase": "^2.30.4", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tinymce": "^7.0.1", "ts-node": "^10.9.2", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@playwright/test": "^1.53.2", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.6.1", "@types/chrome": "^0.0.313", "@types/connect": "^3.4.38", "@types/deno": "^2.2.0", "@types/express": "^5.0.1", "@types/jest": "^29.5.12", "@types/jspdf": "^2.0.0", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/testing-library__jest-dom": "^5.14.9", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.6.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^3.1.1", "autoprefixer": "^10.4.21", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "express": "^5.1.0", "globals": "^15.9.0", "jest-environment-jsdom": "^29.7.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "ts-jest": "^29.1.2", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.19", "vitest": "^3.1.1"}}