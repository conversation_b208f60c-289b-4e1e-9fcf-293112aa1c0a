#!/bin/bash

# Extract all table definitions from the schema backup
echo "-- Extracted table definitions from schema backup"
echo ""

# List of critical tables to extract
tables=(
    "profiles"
    "teams" 
    "team_members"
    "team_properties"
    "properties"
    "maintenance_tasks"
    "damage_reports"
    "inventory_items"
    "purchase_orders"
    "purchase_order_items"
    "invitations"
    "team_invitations"
    "user_permissions"
    "user_preferences"
    "user_settings"
    "service_providers"
    "maintenance_providers"
    "maintenance_requests"
    "property_documents"
    "property_files"
)

for table in "${tables[@]}"; do
    echo "-- Table: $table"
    sed -n "/CREATE TABLE public\.$table/,/^);$/p" db_restore/schema_20250726_041235.sql
    echo ""
done
