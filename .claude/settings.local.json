{"permissions": {"allow": ["Bash(find:*)", "Bash(grep:*)", "Bash(npm test:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(npm run build:*)", "Bash(ls:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./verify-local-dev.sh:*)", "<PERSON><PERSON>(supabase start:*)", "Bash(supabase functions:*)", "Bash(pg_isready:*)", "Bash(psql:*)", "<PERSON><PERSON>(cat:*)", "Bash(npm run dev:*)", "Bash(ss:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git tag:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(for hook in useDamageReportsQueryV2 useMaintenanceTasksQueryV2 usePropertiesQueryV2 useTaskAutomationQueryV2 useTeamManagementQueryV2 useInventoryQueryV2)", "Bash(do echo \"=== $hook ===\")", "Bash(done)", "Bash(cp:*)", "Bash(npm run lint)", "Bash(npm run test:*)", "Bash(supabase status:*)", "Bash(sudo supabase:*)", "<PERSON><PERSON>(npx playwright test:*)", "Bash(node:*)", "Bash(git log:*)"], "deny": []}}