const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function takeScreenshots() {
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: null,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  try {
    // Navigate to the login page
    await page.goto('http://localhost:8081/#/login', { waitUntil: 'networkidle0' });
    
    // Wait for page to load
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Login
    await page.type('input[type="email"]', '<EMAIL>');
    await page.type('input[type="password"]', 'Newsig1!!!');
    await page.click('button[type="submit"]');
    
    // Wait for dashboard to load
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Create screenshots directory if it doesn't exist
    const screenshotsDir = './manual-screenshots';
    if (!fs.existsSync(screenshotsDir)) {
      fs.mkdirSync(screenshotsDir);
    }
    
    // Light mode - Desktop
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
    });
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ 
      path: path.join(screenshotsDir, 'dashboard-light-desktop.png'),
      fullPage: true 
    });
    
    // Dark mode - Desktop
    await page.evaluate(() => {
      document.documentElement.classList.add('dark');
    });
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ 
      path: path.join(screenshotsDir, 'dashboard-dark-desktop.png'),
      fullPage: true 
    });
    
    // Mobile viewport
    await page.setViewport({ width: 375, height: 667 });
    
    // Light mode - Mobile
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
    });
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ 
      path: path.join(screenshotsDir, 'dashboard-light-mobile.png'),
      fullPage: true 
    });
    
    // Dark mode - Mobile
    await page.evaluate(() => {
      document.documentElement.classList.add('dark');
    });
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ 
      path: path.join(screenshotsDir, 'dashboard-dark-mobile.png'),
      fullPage: true 
    });
    
    // Test Properties page
    await page.goto('http://localhost:8081/#/properties');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Light mode
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
    });
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ 
      path: path.join(screenshotsDir, 'properties-light-mobile.png'),
      fullPage: true 
    });
    
    // Dark mode
    await page.evaluate(() => {
      document.documentElement.classList.add('dark');
    });
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ 
      path: path.join(screenshotsDir, 'properties-dark-mobile.png'),
      fullPage: true 
    });
    
    console.log('Screenshots saved to', screenshotsDir);
    
  } catch (error) {
    console.error('Error during testing:', error);
  } finally {
    await browser.close();
  }
}

takeScreenshots();