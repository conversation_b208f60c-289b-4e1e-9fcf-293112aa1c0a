/**
 * Comprehensive Test Suite for AI Improvements
 * 
 * Tests the 4 major AI improvements:
 * 1. Updated Google Generative AI version
 * 2. Conversation memory and context
 * 3. Enhanced voice command browser support
 * 4. Enhanced error messaging with suggestions
 * 
 * Run this in the browser console at http://localhost:8080
 */

const AI_IMPROVEMENTS_TEST = {
  // Test configuration
  config: {
    testUserId: null, // Will be auto-detected
    testCommands: [
      // Valid commands
      "Add a property named Test House at 123 Test Street with 2 bedrooms",
      "Create a maintenance task to fix the sink at Test House",
      "We're down to 1 towel, we need a minimum of 10",
      
      // Invalid commands to test error suggestions
      "add property", // Missing details
      "fix something", // Too vague
      "create order", // Incomplete
      "blah blah blah" // Nonsense
    ]
  },

  // Test conversation memory
  async testConversationMemory() {
    console.log('\n🧠 Testing Conversation Memory...');
    
    // Check if conversation context exists
    const hasConversationContext = typeof window.useAiConversation !== 'undefined';
    console.log(`Conversation Context Available: ${hasConversationContext ? '✅' : '❌'}`);
    
    // Test sequential commands that build on each other
    const sequentialCommands = [
      "Add a property named Memory Test House at 456 Memory Lane with 3 bedrooms",
      "Add a maintenance task for that property to fix the heating",
      "Create inventory for that same property"
    ];
    
    console.log('Testing sequential commands with context...');
    for (let i = 0; i < sequentialCommands.length; i++) {
      const command = sequentialCommands[i];
      console.log(`Command ${i + 1}: "${command}"`);
      
      try {
        const result = await this.sendAICommand(command);
        console.log(`Result: ${result.success ? '✅' : '❌'} ${result.message}`);
      } catch (error) {
        console.log(`Error: ❌ ${error.message}`);
      }
      
      await this.delay(1000);
    }
  },

  // Test voice recognition polyfills
  testVoiceSupport() {
    console.log('\n🎤 Testing Voice Recognition Support...');
    
    // Check browser support detection
    if (typeof window.getBrowserSpeechSupport === 'function') {
      const support = window.getBrowserSpeechSupport();
      console.log(`Browser: ${support.browser}`);
      console.log(`Native Support: ${support.supported ? '✅' : '❌'}`);
      console.log(`Implementation: ${support.implementation}`);
    } else {
      console.log('❌ Voice support detection not available');
    }
    
    // Check if polyfill is loaded
    const hasPolyfill = typeof window.createSpeechRecognition === 'function';
    console.log(`Speech Recognition Polyfill: ${hasPolyfill ? '✅' : '❌'}`);
    
    // Test speech recognition creation
    try {
      if (hasPolyfill) {
        const recognition = window.createSpeechRecognition();
        console.log(`Speech Recognition Creation: ${recognition ? '✅' : '❌'}`);
      }
    } catch (error) {
      console.log(`Speech Recognition Creation: ❌ ${error.message}`);
    }
  },

  // Test error suggestions
  async testErrorSuggestions() {
    console.log('\n💡 Testing Enhanced Error Suggestions...');
    
    // Check if error suggestion utilities are available
    const hasErrorSuggestions = typeof window.getErrorSuggestions === 'function';
    console.log(`Error Suggestions Available: ${hasErrorSuggestions ? '✅' : '❌'}`);
    
    // Test with intentionally bad commands
    const badCommands = [
      "add property", // Missing details
      "fix something", // Too vague  
      "create order", // Incomplete
      "random nonsense command"
    ];
    
    for (const command of badCommands) {
      console.log(`\nTesting error handling for: "${command}"`);
      
      try {
        const result = await this.sendAICommand(command);
        
        if (!result.success) {
          console.log(`❌ Expected error: ${result.message}`);
          
          // Check if suggestions were provided
          if (result.suggestions && result.suggestions.length > 0) {
            console.log(`✅ Suggestions provided: ${result.suggestions.length} items`);
            console.log(`   First suggestion: "${result.suggestions[0]}"`);
          } else {
            console.log(`❌ No suggestions provided`);
          }
        } else {
          console.log(`⚠️ Unexpected success: ${result.message}`);
        }
      } catch (error) {
        console.log(`❌ Test error: ${error.message}`);
      }
      
      await this.delay(500);
    }
  },

  // Test Google AI version upgrade
  async testAIVersionUpgrade() {
    console.log('\n🚀 Testing AI Version Upgrade...');
    
    // Test with a complex command that would benefit from improved AI
    const complexCommand = "Create a comprehensive maintenance schedule for a 3-bedroom oceanfront property including HVAC, plumbing, electrical, and landscaping tasks with seasonal priorities";
    
    console.log(`Testing complex command: "${complexCommand}"`);
    
    try {
      const result = await this.sendAICommand(complexCommand);
      console.log(`Complex Command Result: ${result.success ? '✅' : '❌'} ${result.message}`);
      
      if (result.success) {
        console.log('✅ AI successfully parsed complex command');
      } else {
        console.log('❌ AI struggled with complex command');
        if (result.suggestions) {
          console.log(`Suggestions provided: ${result.suggestions.length}`);
        }
      }
    } catch (error) {
      console.log(`❌ Error testing complex command: ${error.message}`);
    }
  },

  // Helper function to send AI commands
  async sendAICommand(command) {
    // Try to get the Supabase client from the app
    if (typeof window.supabase !== 'undefined') {
      try {
        const { data, error } = await window.supabase.functions.invoke('ai-command-processor', {
          body: {
            command: command,
            userId: this.config.testUserId,
            originalCommand: command,
            hasContext: false
          }
        });

        if (error) {
          throw new Error(error.message);
        }

        return data;
      } catch (error) {
        return {
          success: false,
          message: error.message,
          suggestions: []
        };
      }
    } else {
      // Fallback to direct API call
      try {
        const response = await fetch('/functions/v1/ai-command-processor', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.getAuthToken()}`,
          },
          body: JSON.stringify({
            command: command,
            userId: this.config.testUserId
          })
        });

        const result = await response.json();
        return result;
      } catch (error) {
        return {
          success: false,
          message: error.message,
          suggestions: []
        };
      }
    }
  },

  // Helper to get auth token
  getAuthToken() {
    try {
      const authData = localStorage.getItem('supabase.auth.token');
      if (authData) {
        const parsed = JSON.parse(authData);
        return parsed.access_token;
      }
    } catch (e) {
      // Try alternative methods
    }
    return null;
  },

  // Helper to detect user ID
  detectUserId() {
    try {
      const authData = localStorage.getItem('supabase.auth.token');
      if (authData) {
        const parsed = JSON.parse(authData);
        return parsed.user?.id;
      }
    } catch (e) {
      // Try alternative method
    }
    
    try {
      const user = JSON.parse(localStorage.getItem('supabase.auth.user') || '{}');
      return user.id;
    } catch (e) {
      // No user found
    }
    
    return null;
  },

  // Helper for delays
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  // Run all tests
  async runAllTests() {
    console.log('🎯 AI IMPROVEMENTS COMPREHENSIVE TEST SUITE');
    console.log('='.repeat(50));
    
    // Setup
    this.config.testUserId = this.detectUserId();
    if (!this.config.testUserId) {
      console.log('❌ Could not detect user ID. Please make sure you are logged in.');
      return;
    }
    
    console.log(`✅ User ID detected: ${this.config.testUserId}`);
    
    // Run all test categories
    try {
      await this.testConversationMemory();
      await this.delay(1000);
      
      this.testVoiceSupport();
      await this.delay(1000);
      
      await this.testErrorSuggestions();
      await this.delay(1000);
      
      await this.testAIVersionUpgrade();
      
      console.log('\n✨ All AI improvement tests completed!');
      console.log('='.repeat(50));
      
    } catch (error) {
      console.error('❌ Test suite error:', error);
    }
  }
};

// Make available globally
window.AI_IMPROVEMENTS_TEST = AI_IMPROVEMENTS_TEST;

// Auto-setup message
console.log(`
🤖 AI Improvements Test Suite Loaded!

Run the full test suite:
AI_IMPROVEMENTS_TEST.runAllTests()

Or run individual tests:
AI_IMPROVEMENTS_TEST.testConversationMemory()
AI_IMPROVEMENTS_TEST.testVoiceSupport()
AI_IMPROVEMENTS_TEST.testErrorSuggestions()
AI_IMPROVEMENTS_TEST.testAIVersionUpgrade()

Make sure you're logged in before running tests!
`);

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AI_IMPROVEMENTS_TEST;
}