const puppeteer = require('puppeteer');

async function testTextareaFunctionality() {
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: null,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  try {
    console.log('Testing bulk import textarea functionality...');

    // Login
    await page.goto('http://localhost:8080/#/login', { waitUntil: 'networkidle0' });
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    await page.type('input[type="email"]', '<EMAIL>');
    await page.type('input[type="password"]', 'Newsig1!!!');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Go to inventory page
    await page.goto('http://localhost:8080/#/inventory', { waitUntil: 'networkidle0' });
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Click the "Import" button 
    const bulkImportButton = await page.locator('button:has-text("Import")').first();
    if (await bulkImportButton.count() > 0) {
      await bulkImportButton.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Try to find and interact with the textarea
      const textarea = await page.$('#productIds');
      if (textarea) {
        console.log('Textarea found, testing input...');
        
        // Clear and type in the textarea
        await textarea.click({ clickCount: 3 }); // Select all
        await page.keyboard.press('Delete');
        await textarea.type('B07JW9H4J1');
        
        // Check if the value was set
        const value = await page.evaluate(() => {
          const textarea = document.querySelector('#productIds');
          return textarea ? textarea.value : 'Textarea not found';
        });
        
        console.log('Textarea value after typing:', value);
        
        if (value === 'B07JW9H4J1') {
          console.log('✅ Textarea is working correctly!');
        } else {
          console.log('❌ Textarea not accepting input. Current value:', value);
          
          // Check for any overlays or disabled states
          const isDisabled = await page.evaluate(() => {
            const textarea = document.querySelector('#productIds');
            return textarea ? textarea.disabled : 'unknown';
          });
          
          const isReadOnly = await page.evaluate(() => {
            const textarea = document.querySelector('#productIds');
            return textarea ? textarea.readOnly : 'unknown';
          });
          
          console.log('Is disabled:', isDisabled);
          console.log('Is readonly:', isReadOnly);
        }
      } else {
        console.log('❌ Textarea not found');
      }
    } else {
      console.log('❌ Bulk Import button not found');
    }
    
  } catch (error) {
    console.error('Error during testing:', error);
  } finally {
    await browser.close();
  }
}

testTextareaFunctionality();
