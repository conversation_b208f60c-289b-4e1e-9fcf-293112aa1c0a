const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function takeFullAppScreenshots() {
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: null,
    args: ['--no-sandbox', '--disable-setuid-sandbox', '--start-maximized']
  });
  
  const page = await browser.newPage();
  
  try {
    // Create screenshots directory
    const screenshotsDir = './comprehensive-screenshots';
    if (!fs.existsSync(screenshotsDir)) {
      fs.mkdirSync(screenshotsDir);
    }

    // Helper function to wait and take screenshot
    const takeScreenshot = async (name, theme = 'light') => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await page.screenshot({ 
        path: path.join(screenshotsDir, `${name}-${theme}.png`),
        fullPage: true 
      });
      console.log(`Screenshot taken: ${name}-${theme}.png`);
    };

    // Helper function to switch themes
    const switchTheme = async (theme) => {
      if (theme === 'dark') {
        await page.evaluate(() => {
          document.documentElement.classList.add('dark');
        });
      } else {
        await page.evaluate(() => {
          document.documentElement.classList.remove('dark');
        });
      }
      await new Promise(resolve => setTimeout(resolve, 1000));
    };

    // Helper function to test both themes
    const testBothThemes = async (name, actionCallback = null) => {
      // Light theme
      await switchTheme('light');
      if (actionCallback) await actionCallback();
      await takeScreenshot(name, 'light');
      
      // Dark theme
      await switchTheme('dark');
      if (actionCallback) await actionCallback();
      await takeScreenshot(name, 'dark');
    };

    console.log('Starting comprehensive app testing...');

    // 1. LOGIN PAGE
    console.log('Testing login page...');
    await page.goto('http://localhost:8081/#/login', { waitUntil: 'networkidle0' });
    await testBothThemes('01-login-page');

    // Login
    await page.type('input[type="email"]', '<EMAIL>');
    await page.type('input[type="password"]', 'Newsig1!!!');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 2. DASHBOARD
    console.log('Testing dashboard...');
    await testBothThemes('02-dashboard');

    // Test mobile viewport for dashboard
    await page.setViewport({ width: 375, height: 667 });
    await testBothThemes('03-dashboard-mobile');

    // Reset to desktop viewport
    await page.setViewport({ width: 1280, height: 720 });

    // 3. PROPERTIES PAGE
    console.log('Testing properties page...');
    await page.goto('http://localhost:8081/#/properties', { waitUntil: 'networkidle0' });
    await testBothThemes('04-properties-page');

    // Try to click on a property card to open details
    try {
      const propertyCards = await page.$$('.cursor-pointer');
      if (propertyCards.length > 0) {
        await propertyCards[0].click();
        await new Promise(resolve => setTimeout(resolve, 2000));
        await testBothThemes('05-property-details-modal');
        
        // Close modal
        await page.keyboard.press('Escape');
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.log('No property cards found or modal failed to open');
    }

    // 4. MAINTENANCE PAGE
    console.log('Testing maintenance page...');
    await page.goto('http://localhost:8081/#/maintenance', { waitUntil: 'networkidle0' });
    await testBothThemes('06-maintenance-page');

    // Try to open maintenance task creation modal
    try {
      const addButtons = await page.$$('button');
      const addButton = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button'));
        return buttons.find(btn => btn.textContent.includes('Add') || btn.textContent.includes('New') || btn.textContent.includes('Create'));
      });
      
      if (addButton) {
        await page.click('button');
        await new Promise(resolve => setTimeout(resolve, 2000));
        await testBothThemes('07-maintenance-add-modal');
        
        // Close modal
        await page.keyboard.press('Escape');
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.log('Could not open maintenance modal');
    }

    // 5. INVENTORY PAGE
    console.log('Testing inventory page...');
    await page.goto('http://localhost:8081/#/inventory', { waitUntil: 'networkidle0' });
    await testBothThemes('08-inventory-page');

    // 6. PURCHASE ORDERS PAGE
    console.log('Testing purchase orders page...');
    await page.goto('http://localhost:8081/#/purchase-orders', { waitUntil: 'networkidle0' });
    await testBothThemes('09-purchase-orders-page');

    // 7. DAMAGES PAGE
    console.log('Testing damages page...');
    await page.goto('http://localhost:8081/#/damages', { waitUntil: 'networkidle0' });
    await testBothThemes('10-damages-page');

    // 8. OPERATIONS PAGE
    console.log('Testing operations page...');
    await page.goto('http://localhost:8081/#/operations', { waitUntil: 'networkidle0' });
    await testBothThemes('11-operations-page');

    // 9. SETTINGS PAGE
    console.log('Testing settings page...');
    await page.goto('http://localhost:8081/#/settings', { waitUntil: 'networkidle0' });
    await testBothThemes('12-settings-page');

    // 10. Test Navigation Menu (Mobile)
    console.log('Testing mobile navigation...');
    await page.setViewport({ width: 375, height: 667 });
    await page.goto('http://localhost:8081/#/dashboard', { waitUntil: 'networkidle0' });
    
    // Try to open mobile menu
    try {
      const menuButton = await page.$('button[aria-label="Menu"]') || 
                         await page.$('button[aria-label="Open menu"]') ||
                         await page.$('.hamburger') ||
                         await page.$('[data-testid="menu-button"]');
      
      if (menuButton) {
        await menuButton.click();
        await new Promise(resolve => setTimeout(resolve, 1000));
        await testBothThemes('13-mobile-menu');
        
        // Close menu
        await page.keyboard.press('Escape');
      }
    } catch (error) {
      console.log('Could not open mobile menu');
    }

    // 11. Test Forms and Inputs
    console.log('Testing form inputs...');
    await page.goto('http://localhost:8081/#/properties', { waitUntil: 'networkidle0' });
    
    // Try to find and focus on search/filter inputs
    try {
      const searchInput = await page.$('input[type="search"]') || 
                         await page.$('input[placeholder*="search" i]') ||
                         await page.$('input[placeholder*="filter" i]');
      
      if (searchInput) {
        await searchInput.focus();
        await new Promise(resolve => setTimeout(resolve, 500));
        await testBothThemes('14-form-inputs-focused');
      }
    } catch (error) {
      console.log('Could not find search inputs');
    }

    // 12. Test Sidebar States
    console.log('Testing sidebar states...');
    await page.setViewport({ width: 1280, height: 720 });
    await page.goto('http://localhost:8081/#/dashboard', { waitUntil: 'networkidle0' });
    
    // Try to toggle sidebar if possible
    try {
      const sidebarToggle = await page.$('[data-testid="sidebar-toggle"]') ||
                           await page.$('.sidebar-toggle') ||
                           await page.$('button[aria-label*="sidebar" i]');
      
      if (sidebarToggle) {
        await sidebarToggle.click();
        await new Promise(resolve => setTimeout(resolve, 1000));
        await testBothThemes('15-sidebar-collapsed');
        
        // Toggle back
        await sidebarToggle.click();
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.log('Could not toggle sidebar');
    }

    // 13. Test Error States and Loading States
    console.log('Testing loading states...');
    await page.goto('http://localhost:8081/#/dashboard', { waitUntil: 'networkidle0' });
    
    // Simulate slow network to capture loading states
    await page.setRequestInterception(true);
    page.on('request', (req) => {
      if (req.url().includes('api') || req.url().includes('supabase')) {
        setTimeout(() => req.continue(), 2000); // Delay API calls
      } else {
        req.continue();
      }
    });
    
    await page.reload();
    await new Promise(resolve => setTimeout(resolve, 3000));
    await testBothThemes('16-loading-states');

    // Reset request interception
    await page.setRequestInterception(false);

    // 14. Test Responsive Breakpoints
    console.log('Testing responsive breakpoints...');
    const breakpoints = [
      { width: 320, height: 568, name: 'mobile-small' },
      { width: 375, height: 667, name: 'mobile-medium' },
      { width: 768, height: 1024, name: 'tablet' },
      { width: 1024, height: 768, name: 'desktop-small' },
      { width: 1440, height: 900, name: 'desktop-large' }
    ];

    for (const bp of breakpoints) {
      await page.setViewport({ width: bp.width, height: bp.height });
      await page.goto('http://localhost:8081/#/dashboard', { waitUntil: 'networkidle0' });
      await testBothThemes(`17-responsive-${bp.name}`);
    }

    console.log('Comprehensive testing completed!');
    console.log(`Screenshots saved to ${screenshotsDir}`);
    
  } catch (error) {
    console.error('Error during comprehensive testing:', error);
  } finally {
    await browser.close();
  }
}

takeFullAppScreenshots();