# Environment Configuration Summary

This document provides a comprehensive overview of the StayFu application's environment configuration setup, implemented to ensure proper development workflows across different environments.

## 🎯 Configuration Overview

The environment setup has been configured to support three distinct development modes:

### ✅ **`npm run dev`** (Default Development)
- **Uses**: `.env` file
- **Connects to**: Production Supabase database
- **Purpose**: Default development behavior that works on both local machines and Vercel
- **Recommended for**: Most feature development work
- **No additional setup required**

### ✅ **`npm run dev:local`** (100% Local Development)
- **Uses**: `.env.development` file (`--mode development`)
- **Connects to**: Local Supabase instance (127.0.0.1:54321)
- **Purpose**: 100% local development with local database
- **Recommended for**: Schema changes, migrations, testing without affecting production
- **Requires**: Local Supabase running (`supabase start`)

### ✅ **`npm run dev:remote`** (Explicit Remote)
- **Uses**: `.env.production` file (`--mode production`)
- **Connects to**: Production Supabase database
- **Purpose**: Explicit connection to production database
- **Recommended for**: Final testing before deployment

## 📁 Environment Files Structure

### `.env` (Default - Committed)
```bash
# Default Environment Configuration
# Used by npm run dev - works for local development and Vercel deployment
VITE_SUPABASE_URL=https://pwaeknalhosfwuxkpaet.supabase.co
VITE_SUPABASE_PUBLISHABLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
VITE_CHROME_EXTENSION_ID=ghjonlbpfliehedepdgopgjfonageedm
NODE_ENV=development
SUPABASE_PROJECT_REF=pwaeknalhosfwuxkpaet
```

### `.env.development` (Local - Committed)
```bash
# Local Development Environment Configuration
# Used by npm run dev:local (100% local Supabase)
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_PUBLISHABLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
VITE_CHROME_EXTENSION_ID=ghjonlbpfliehedepdgopgjfonageedm
NODE_ENV=development
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_PROJECT_REF=stayfuse
```

### `.env.production` (Production - Committed)
```bash
# Production Supabase Configuration (Remote)
VITE_SUPABASE_URL=https://pwaeknalhosfwuxkpaet.supabase.co
VITE_SUPABASE_PUBLISHABLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
VITE_CHROME_EXTENSION_ID=ghjonlbpfliehedepdgopgjfonageedm
NODE_ENV=production
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_PROJECT_REF=pwaeknalhosfwuxkpaet
```

### `.env.local` (Optional - Gitignored)
```bash
# Local Development Environment Configuration
# This file is gitignored and can be used for developer-specific overrides
# Copy from .env.development if you need local-only customizations
```

## 🔧 Key Improvements Implemented

### 1. **Fixed Environment Files**
- ✅ Correct production service key retrieved from Supabase API
- ✅ Proper environment variable naming and structure
- ✅ Clear separation between local and production configurations
- ✅ Optional `.env.local` for developer-specific overrides

### 2. **Updated Package.json Scripts**
```json
{
  "scripts": {
    "dev": "vite",                    // Default - uses .env
    "dev:local": "vite --mode development",   // Uses .env.development
    "dev:remote": "vite --mode production"    // Uses .env.production
  }
}
```

### 3. **Enhanced Supabase Client Configuration**
- ✅ Automatic environment detection based on URL
- ✅ Improved console logging for debugging
- ✅ Better error handling and fallbacks
- ✅ Environment-specific configuration display

### 4. **Fixed Service Worker Caching Issues**
- ✅ Incremented cache version from v7 to v8
- ✅ Forces browsers to download updated JavaScript files
- ✅ Resolves issues with cached old code being served

### 5. **Vite Configuration Updates**
- ✅ Updated to handle environment modes properly
- ✅ Added environment-specific build configuration
- ✅ Proper mode detection and variable injection

## 🚀 Development Workflow

### Standard Development (Recommended)
```bash
npm run dev
```
- Uses production database by default
- Good for most feature development
- No local Supabase setup required
- Works identically on local machines and Vercel

### Local Database Testing
```bash
supabase start          # Start local Supabase
npm run dev:local       # Connect to local instance
```
- Uses local Supabase instance
- Good for schema changes or testing migrations
- Requires Docker and Supabase CLI
- 100% isolated from production

### Production Testing
```bash
npm run dev:remote      # Explicitly use production
```
- Explicitly connects to production database
- Good for final testing before deployment
- Same as default `npm run dev` but explicit

## 🔍 Environment Detection & Debugging

### Browser Console Output
When the app loads, check the browser console for:
```
🔧 Supabase Configuration:
Vite Mode: development/production/default
Environment: local/production
URL: http://127.0.0.1:54321 or https://pwaeknalhosfwuxkpaet.supabase.co
Key prefix: eyJhbGciOiJIUzI1NiIsI...
Environment variables:
- VITE_SUPABASE_URL: Set/Using default
- VITE_SUPABASE_PUBLISHABLE_KEY: Set/Using default
```

### Automatic Environment Detection
The Supabase client automatically detects:
- **Local**: URLs containing `127.0.0.1` or `localhost`
- **Production**: URLs containing `supabase.co`
- **Unknown**: Any other configuration

## 🌐 Vercel Deployment

Vercel will automatically use the `.env` file, but you can override variables in the Vercel dashboard:
- `VITE_SUPABASE_URL`
- `VITE_SUPABASE_PUBLISHABLE_KEY`
- `VITE_CHROME_EXTENSION_ID`

## 🛠️ Troubleshooting

### Environment Variables Not Loading
1. Restart the development server
2. Check that the correct `.env.*` file exists
3. Verify environment variables are prefixed with `VITE_`
4. Clear browser cache and hard refresh

### Service Worker Cache Issues
1. Hard refresh browser (Ctrl+Shift+R)
2. Check if service worker cache version updated
3. Clear browser application data if needed

### Database Connection Issues
1. Check browser console for Supabase configuration
2. Verify the correct environment is being used
3. For local: ensure `supabase start` is running
4. For production: verify network connectivity

### Profile/Auth Issues in Local Development
If you see profile-related errors when running `npm run dev:local`:

1. **Missing Profiles**: Local database may have users without corresponding profiles
   ```bash
   # Fix by creating missing profiles
   psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -c "
   INSERT INTO profiles (id, first_name, last_name, email, role, created_at, updated_at)
   SELECT
     au.id,
     COALESCE(au.raw_user_meta_data->>'first_name', 'User') as first_name,
     COALESCE(au.raw_user_meta_data->>'last_name', 'Name') as last_name,
     au.email,
     COALESCE(au.raw_user_meta_data->>'role', 'property_manager')::user_role as role,
     au.created_at,
     au.updated_at
   FROM auth.users au
   LEFT JOIN profiles p ON au.id = p.id
   WHERE p.id IS NULL AND au.email_confirmed_at IS NOT NULL;
   "
   ```

2. **Test Local Auth**: Run the test script to verify everything works
   ```bash
   node tests/test-local-auth.mjs
   ```

### Database Synchronization Issues
If your local database is missing tables or data that exists in the cloud:

1. **Missing Tables** (e.g., `profiles` table doesn't exist):
   ```sql
   -- Create user_role enum if it doesn't exist
   DO $$
   BEGIN
       IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
           CREATE TYPE user_role AS ENUM ('property_manager', 'service_provider', 'super_admin');
       END IF;
   END
   $$;

   -- Create profiles table
   CREATE TABLE IF NOT EXISTS profiles (
     id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
     first_name TEXT,
     last_name TEXT,
     email TEXT,
     avatar_url TEXT,
     is_super_admin BOOLEAN DEFAULT FALSE,
     role user_role DEFAULT 'property_manager',
     created_at TIMESTAMPTZ DEFAULT NOW(),
     updated_at TIMESTAMPTZ DEFAULT NOW()
   );
   ```

2. **Missing User Data**: Add specific users from cloud to local database:
   ```sql
   -- Add user to auth.users (replace with actual data)
   INSERT INTO auth.users (id, email, raw_user_meta_data, created_at, updated_at, ...)
   VALUES ('user-id', '<EMAIL>', '{"first_name": "Name"}', NOW(), NOW(), ...);

   -- Add corresponding profile
   INSERT INTO profiles (id, first_name, last_name, email, role, created_at, updated_at)
   VALUES ('user-id', 'First', 'Last', '<EMAIL>', 'service_provider', NOW(), NOW());
   ```

## 📝 Maintenance Notes

- **Service Worker Cache**: Increment version in `public/service-worker.js` when making significant changes
- **Environment Keys**: Update production keys via Supabase dashboard when needed
- **Local Development**: Keep local Supabase instance updated with `supabase db pull`
- **Documentation**: Update this file when making environment configuration changes

---

**Last Updated**: January 2025
**Configuration Version**: v8 (Service Worker Cache)
**Supabase Project**: pwaeknalhosfwuxkpaet (stayfulove)
