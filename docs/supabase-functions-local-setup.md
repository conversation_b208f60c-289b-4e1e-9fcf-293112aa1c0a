# Supabase Functions Local Development Setup

## Overview

We have successfully created a sandboxed local development environment with all Supabase Edge Functions from production. This allows you to develop and test functions locally without affecting the production environment.

## What Was Done

### 1. Function Discovery
- Retrieved list of all 38 Edge Functions from production Supabase project
- Identified function metadata including IDs, versions, and JWT verification requirements

### 2. Local Function Templates Created
All production functions have been recreated locally with template code:

#### Admin Functions (9)
- `admin-backup-database` - Database backup functionality
- `admin-create-user` - User creation
- `admin-delete-user` - User deletion
- `admin-force-delete-user` - Force user deletion
- `admin-get-all-users` - Get all users
- `admin-get-upload-url` - Generate upload URLs
- `admin-get-users` - Get specific users
- `admin-restore-database` - Database restoration
- `admin-update-user` - User updates

#### Core Application Functions (12)
- `get-user-by-id` - User retrieval by ID
- `upload-damage-photo` - Damage photo uploads
- `upload-property-image` - Property image uploads
- `upload-damage-video` - Damage video uploads
- `upload-inventory-image` - Inventory image uploads
- `upload-invoice-pdf` - Invoice PDF uploads
- `fetch-ical-data` - Calendar data fetching
- `sync-property-calendars` - Calendar synchronization
- `scrape-product` - Product scraping
- `send-email` - Email sending
- `maintenance-response` - Maintenance responses
- `manage-user` - User management

#### AI Functions (2)
- `ai-command-processor` - AI command processing (existing code preserved)
- `ai-maintenance-items` - AI maintenance item processing

#### Team & Invitation Functions (5)
- `create-team-invitation` - Team invitation creation
- `get-invitation-details` - Invitation details retrieval
- `accept-invitation` - Invitation acceptance
- `accept-invitation-direct` - Direct invitation acceptance
- `register-service-provider` - Service provider registration

#### Data Access Functions (5)
- `get-maintenance-tasks` - Maintenance task retrieval
- `get-team-maintenance-tasks` - Team maintenance tasks
- `get-team-data` - Team data retrieval
- `get-property-manager-teams` - Property manager teams
- `process-recurring-tasks` - Recurring task processing

#### Utility Functions (5)
- `verify-maintenance-token` - Token verification
- `generate-extension-token` - Extension token generation
- `create-storage-buckets` - Storage bucket creation
- `execute-sql` - SQL execution
- `fix-missing-profiles` - Profile fixing

## Directory Structure

```
supabase/functions/
├── accept-invitation/
│   ├── deno.json
│   └── index.ts
├── accept-invitation-direct/
│   ├── deno.json
│   └── index.ts
├── admin-backup-database/
│   ├── deno.json
│   └── index.ts
├── ai-command-processor/
│   ├── index.ts
│   └── intelligentSuggestions.ts
└── ... (35 more functions)
```

## Template Structure

Each function template includes:

1. **deno.json** - Deno configuration with Supabase imports
2. **index.ts** - Function template with:
   - Production metadata (ID, version, JWT verification)
   - CORS headers setup
   - Supabase client initialization
   - Error handling structure
   - Placeholder for actual implementation

## Backup Information

- **Backup Location**: `.supabase/backups/functions/2025-07-25T23-09-36`
- **Existing Functions**: The `ai-command-processor` function was preserved as it already contained actual code

## Next Steps

### 1. Replace Templates with Production Code

For each function, you need to:
1. Identify the actual production implementation
2. Replace the template code with the real function logic
3. Test the function locally

### 2. Local Development Workflow

```bash
# Start local Supabase (if not already running)
supabase start

# Serve functions locally
supabase functions serve

# Test a specific function
curl -X POST 'http://localhost:54321/functions/v1/function-name' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{"test": "data"}'
```

### 3. Deployment Workflow

```bash
# Deploy a single function
supabase functions deploy function-name

# Deploy all functions
supabase functions deploy
```

### 4. Getting Production Code

To get the actual production code for each function, you can:

1. **Check Git History**: Look for the original function implementations in your git history
2. **Export from Production**: Use Supabase CLI or API to export function source (if available)
3. **Recreate from Documentation**: Use API documentation and business logic to recreate functions
4. **Team Collaboration**: Work with team members who have the original source code

## Environment Variables

Make sure your local environment has the necessary environment variables:

```bash
# In your .env file or Supabase config
SUPABASE_URL=http://127.0.0.1:54321
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Testing Strategy

1. **Unit Testing**: Test individual functions with various inputs
2. **Integration Testing**: Test functions with actual database operations
3. **End-to-End Testing**: Test complete workflows using the functions

## Security Considerations

- Functions marked with `verify_jwt: true` require authentication
- Functions marked with `verify_jwt: false` are publicly accessible
- Always validate inputs and sanitize outputs
- Use appropriate Supabase client (anon vs service role) based on function requirements

## Troubleshooting

### Common Issues

1. **Import Errors**: Check deno.json imports
2. **Authentication Errors**: Verify JWT verification settings
3. **Database Errors**: Ensure local Supabase is running and configured
4. **CORS Errors**: Check CORS headers in function responses

### Useful Commands

```bash
# Check function logs
supabase functions logs function-name

# Check local Supabase status
supabase status

# Reset local environment
supabase db reset
```

## Resources

- [Supabase Edge Functions Documentation](https://supabase.com/docs/guides/functions)
- [Deno Documentation](https://deno.land/manual)
- [Local Development Guide](./supabase-environment-setup.md)
