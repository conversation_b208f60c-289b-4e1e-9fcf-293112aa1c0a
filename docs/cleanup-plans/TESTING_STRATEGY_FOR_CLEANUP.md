# Testing Strategy for Safe Codebase Cleanup

## Current Testing State Analysis

### Critical Issues Identified

#### 1. **Minimal Test Coverage**
- Only **1 working test** (`src/working.test.js`) with basic functionality
- **25+ test files exist** but many appear broken or incomplete
- Multiple test frameworks configured but not properly integrated:
  - Jest (configured but only runs 1 file)
  - <PERSON><PERSON> (configured for E2E)
  - Cypress (configured but screenshots show failures)

#### 2. **Broken Test Infrastructure**
- Jest config only targets `src/working.test.js`
- Component tests reference missing test utilities
- No proper mocking setup for Supabase/API calls
- Test environments not properly configured

#### 3. **No Integration Testing**
- No tests covering user workflows
- No API integration tests
- No database interaction tests
- No testing of critical business logic

## Pre-Cleanup Testing Requirements

### Phase 0: Establish Testing Baseline (BEFORE any cleanup)

#### 0.1 Fix Current Test Infrastructure

```bash
# 1. Update Jest configuration
# jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  testMatch: [
    '<rootDir>/src/**/*.test.{js,jsx,ts,tsx}',
    '<rootDir>/tests/**/*.test.{js,jsx,ts,tsx}'
  ],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
    '^.+\\.(js|jsx)$': 'babel-jest',
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/main.tsx',
    '!src/vite-env.d.ts',
  ],
  coverageReporters: ['text', 'lcov', 'html'],
  coverageDirectory: 'coverage',
};
```

#### 0.2 Create Essential Test Setup

```typescript
// tests/setup.ts
import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';
import { afterEach, beforeAll, afterAll } from 'vitest';
import { server } from './mocks/server';

// Establish API mocking
beforeAll(() => server.listen());
afterEach(() => {
  cleanup();
  server.resetHandlers();
});
afterAll(() => server.close());

// Mock Supabase client
jest.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getSession: jest.fn(),
      onAuthStateChange: jest.fn(),
      signInWithPassword: jest.fn(),
      signOut: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      single: jest.fn(),
    })),
  },
}));
```

#### 0.3 Critical Functionality Snapshot Tests

**BEFORE making ANY changes**, create comprehensive snapshot tests:

```typescript
// tests/snapshots/critical-functionality.test.tsx
import { render } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Test all major page components in their current state
import Dashboard from '@/pages/Dashboard';
import Properties from '@/pages/Properties';
import Inventory from '@/pages/Inventory';
import Maintenance from '@/pages/Maintenance';

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } }
  });
  
  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </BrowserRouter>
  );
};

describe('Critical Functionality Snapshots - BASELINE', () => {
  it('Dashboard renders without crashing', () => {
    const { container } = render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('Properties page renders without crashing', () => {
    const { container } = render(
      <TestWrapper>
        <Properties />
      </TestWrapper>
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('Inventory page renders without crashing', () => {
    const { container } = render(
      <TestWrapper>
        <Inventory />
      </TestWrapper>
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('Maintenance page renders without crashing', () => {
    const { container } = render(
      <TestWrapper>
        <Maintenance />
      </TestWrapper>
    );
    expect(container.firstChild).toMatchSnapshot();
  });
});
```

## Testing Strategy by Cleanup Phase

### Phase 1: Foundation Cleanup Testing

#### 1.1 Pre-Phase Tests
```bash
# Run full test suite BEFORE any changes
npm run test:all
npm run test:coverage
npm run test:e2e

# Create baseline snapshots
npm run test:snapshots
```

#### 1.2 During Foundation Cleanup
**Safe Changes Only:**
- File moves/renames
- Documentation consolidation  
- Dead file removal

**Required Tests After Each Change:**
```bash
# After each file move/rename
npm run test:snapshots -- --updateSnapshot=false
npm run build
npm run dev # Verify app still starts

# Automated check script
./scripts/verify-no-regression.sh
```

#### 1.3 Automated Regression Detection

```bash
# scripts/verify-no-regression.sh
#!/bin/bash
set -e

echo "🔍 Running regression checks..."

# 1. Build check
echo "Building application..."
npm run build

# 2. Start dev server in background
echo "Starting dev server..."
npm run dev &
DEV_PID=$!
sleep 10

# 3. Quick smoke tests
echo "Running smoke tests..."
npx playwright test --grep="smoke"

# 4. API tests
echo "Testing API endpoints..."
npm run test:api

# 5. Cleanup
kill $DEV_PID

echo "✅ No regressions detected"
```

### Phase 2: Database Schema Testing

#### 2.1 Database Migration Safety

```typescript
// tests/database/migration-safety.test.ts
describe('Database Migration Safety', () => {
  beforeEach(async () => {
    // Create test data in current schema
    await createTestData();
  });

  it('should preserve all existing data during migration', async () => {
    const beforeData = await getAllTestData();
    
    // Run migration
    await runMigration('001_optimize_inventory');
    
    const afterData = await getAllTestData();
    
    // Verify no data loss
    expect(afterData.properties).toHaveLength(beforeData.properties.length);
    expect(afterData.inventory).toHaveLength(beforeData.inventory.length);
    expect(afterData.users).toHaveLength(beforeData.users.length);
  });

  it('should maintain all foreign key relationships', async () => {
    await runMigration('001_optimize_inventory');
    
    // Test critical relationships
    const propertiesWithInventory = await supabase
      .from('properties')
      .select(`
        *,
        inventory_items(*)
      `);

    expect(propertiesWithInventory.data).toBeDefined();
    expect(propertiesWithInventory.error).toBeNull();
  });

  it('should rollback cleanly on failure', async () => {
    const beforeData = await getAllTestData();
    
    try {
      await runFailingMigration();
    } catch (error) {
      // Migration should fail and rollback
    }
    
    const afterData = await getAllTestData();
    
    // Data should be unchanged
    expect(afterData).toEqual(beforeData);
  });
});
```

#### 2.2 RLS Policy Testing

```typescript
// tests/database/rls-policies.test.ts
describe('Row Level Security Policies', () => {
  const testUsers = {
    admin: { id: 'admin-user-id', role: 'admin' },
    manager: { id: 'manager-user-id', role: 'property_manager' },
    staff: { id: 'staff-user-id', role: 'staff' },
    guest: { id: 'guest-user-id', role: 'guest' }
  };

  describe('Properties Access', () => {
    it('admin can access all team properties', async () => {
      const { data } = await supabase
        .from('properties')
        .select('*')
        .eq('team_id', 'test-team-id');
        
      expect(data).toHaveLength(expectedPropertiesCount);
    });

    it('staff can only access assigned properties', async () => {
      // Test with staff user context
      const { data } = await supabaseAsUser(testUsers.staff)
        .from('properties')
        .select('*');
        
      expect(data).toHaveLength(expectedStaffPropertiesCount);
    });
  });
});
```

### Phase 3: Component Migration Testing

#### 3.1 Component Compatibility Testing

```typescript
// tests/components/migration-compatibility.test.tsx
describe('Component Migration Compatibility', () => {
  describe('Button Migration', () => {
    it('old button props should work with new Button component', () => {
      // Test backward compatibility
      const { getByRole } = render(
        <Button 
          variant="primary" 
          size="md" 
          onClick={jest.fn()}
        >
          Test Button
        </Button>
      );
      
      expect(getByRole('button')).toBeInTheDocument();
    });

    it('should maintain same visual appearance', () => {
      const { container: oldContainer } = render(<OldButton />);
      const { container: newContainer } = render(<NewButton />);
      
      // Visual regression test
      expect(newContainer.firstChild).toHaveClass(
        ...expectedButtonClasses
      );
    });
  });

  describe('Form Migration', () => {
    it('should maintain form validation behavior', async () => {
      const onSubmit = jest.fn();
      const { getByRole, getByLabelText } = render(
        <NewPropertyForm onSubmit={onSubmit} />
      );

      // Fill invalid data
      fireEvent.change(getByLabelText(/property name/i), {
        target: { value: '' }
      });

      fireEvent.click(getByRole('button', { name: /save/i }));

      // Should show validation error
      await waitFor(() => {
        expect(screen.getByText(/property name is required/i))
          .toBeInTheDocument();
      });

      expect(onSubmit).not.toHaveBeenCalled();
    });
  });
});
```

#### 3.2 API Integration Testing

```typescript
// tests/integration/api-integration.test.ts
describe('API Integration During Migration', () => {
  it('should maintain property CRUD operations', async () => {
    // Create property
    const newProperty = await propertyService.createProperty({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'house'
    });

    expect(newProperty.id).toBeDefined();

    // Read property
    const fetchedProperty = await propertyService.getProperty(newProperty.id);
    expect(fetchedProperty.name).toBe('Test Property');

    // Update property
    const updatedProperty = await propertyService.updateProperty(
      newProperty.id,
      { name: 'Updated Property' }
    );
    expect(updatedProperty.name).toBe('Updated Property');

    // Delete property
    await propertyService.deleteProperty(newProperty.id);
    
    await expect(
      propertyService.getProperty(newProperty.id)
    ).rejects.toThrow();
  });

  it('should maintain team access controls', async () => {
    const teamAProperty = await createPropertyInTeam('team-a');
    const teamBProperty = await createPropertyInTeam('team-b');

    // User in team A should only see team A properties
    const teamAUser = await authenticateAs('team-a-user');
    const properties = await propertyService.getProperties();

    expect(properties).toContainEqual(
      expect.objectContaining({ id: teamAProperty.id })
    );
    expect(properties).not.toContainEqual(
      expect.objectContaining({ id: teamBProperty.id })
    );
  });
});
```

### Phase 4: End-to-End Workflow Testing

#### 4.1 Critical User Journey Tests

```typescript
// tests/e2e/critical-workflows.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Critical User Workflows', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('[data-testid=email]', '<EMAIL>');
    await page.fill('[data-testid=password]', 'password123');
    await page.click('[data-testid=login-button]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('Complete Property Management Workflow', async ({ page }) => {
    // Navigate to properties
    await page.click('[data-testid=nav-properties]');
    
    // Create new property
    await page.click('[data-testid=add-property]');
    await page.fill('[data-testid=property-name]', 'E2E Test Property');
    await page.fill('[data-testid=property-address]', '123 Test Ave');
    await page.click('[data-testid=save-property]');
    
    // Verify property created
    await expect(page.locator('[data-testid=property-card]'))
      .toContainText('E2E Test Property');
    
    // Edit property
    await page.click('[data-testid=property-menu]');
    await page.click('[data-testid=edit-property]');
    await page.fill('[data-testid=property-name]', 'Updated Property');
    await page.click('[data-testid=save-property]');
    
    // Verify update
    await expect(page.locator('[data-testid=property-card]'))
      .toContainText('Updated Property');
  });

  test('Inventory Management Workflow', async ({ page }) => {
    await page.goto('/inventory');
    
    // Add inventory item
    await page.click('[data-testid=add-inventory]');
    await page.fill('[data-testid=item-name]', 'Test Item');
    await page.fill('[data-testid=quantity]', '10');
    await page.click('[data-testid=save-item]');
    
    // Verify item added
    await expect(page.locator('[data-testid=inventory-item]'))
      .toContainText('Test Item');
    
    // Update quantity
    await page.click('[data-testid=item-actions]');
    await page.click('[data-testid=update-quantity]');
    await page.fill('[data-testid=new-quantity]', '15');
    await page.click('[data-testid=save-quantity]');
    
    // Verify quantity updated
    await expect(page.locator('[data-testid=item-quantity]'))
      .toContainText('15');
  });

  test('Team Management Workflow', async ({ page }) => {
    await page.goto('/teams');
    
    // Invite team member
    await page.click('[data-testid=invite-member]');
    await page.fill('[data-testid=member-email]', '<EMAIL>');
    await page.selectOption('[data-testid=member-role]', 'staff');
    await page.click('[data-testid=send-invitation]');
    
    // Verify invitation sent
    await expect(page.locator('[data-testid=success-message]'))
      .toContainText('Invitation sent');
    
    // Check pending invitations
    await expect(page.locator('[data-testid=pending-invitation]'))
      .toContainText('<EMAIL>');
  });
});
```

#### 4.2 Performance Regression Testing

```typescript
// tests/performance/performance-regression.test.ts
describe('Performance Regression Tests', () => {
  it('should load property list within acceptable time', async () => {
    const startTime = performance.now();
    
    await page.goto('/properties');
    await page.waitForSelector('[data-testid=property-list]');
    
    const loadTime = performance.now() - startTime;
    
    // Should load within 3 seconds
    expect(loadTime).toBeLessThan(3000);
  });

  it('should handle large datasets efficiently', async () => {
    // Create 1000 test properties
    await createTestProperties(1000);
    
    const startTime = performance.now();
    
    const { data } = await supabase
      .from('properties')
      .select('*')
      .limit(50);
    
    const queryTime = performance.now() - startTime;
    
    // Query should complete within 500ms
    expect(queryTime).toBeLessThan(500);
    expect(data).toHaveLength(50);
  });
});
```

## Continuous Testing During Cleanup

### Automated Testing Pipeline

```yaml
# .github/workflows/cleanup-testing.yml
name: Cleanup Testing Pipeline

on:
  pull_request:
    branches: [main]
  push:
    branches: [cleanup/*]

jobs:
  test-matrix:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        test-type: [unit, integration, e2e, performance]
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          
      - name: Install dependencies
        run: npm ci
        
      - name: Start Supabase
        run: npx supabase start
        
      - name: Run tests
        run: npm run test:${{ matrix.test-type }}
        
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        if: matrix.test-type == 'unit'

  visual-regression:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install Playwright
        run: npx playwright install
        
      - name: Run visual regression tests
        run: npx playwright test --grep="visual"
        
      - name: Upload screenshots
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: screenshots
          path: tests/screenshots/
```

### Testing Checklist for Each Cleanup Phase

#### Before Starting Any Phase:
- [ ] Run full test suite and record baseline results
- [ ] Create snapshots of current functionality
- [ ] Document current API behavior
- [ ] Record performance benchmarks

#### During Each Phase:
- [ ] Run tests after each significant change
- [ ] Update tests to match new patterns (if needed)
- [ ] Verify no functionality regression
- [ ] Check performance hasn't degraded

#### After Each Phase:
- [ ] Full regression test suite
- [ ] E2E workflow testing
- [ ] Performance benchmark comparison
- [ ] Manual testing of critical paths

### Risk Mitigation Strategies

#### 1. **Feature Flags for Gradual Rollout**
```typescript
// lib/feature-flags.ts
export const featureFlags = {
  useNewButton: process.env.REACT_APP_USE_NEW_BUTTON === 'true',
  useNewFormSystem: process.env.REACT_APP_USE_NEW_FORMS === 'true',
  useNewApiLayer: process.env.REACT_APP_USE_NEW_API === 'true',
};

// Gradual component migration
export const Button = featureFlags.useNewButton ? NewButton : OldButton;
```

#### 2. **Rollback Procedures**
```bash
# scripts/rollback.sh
#!/bin/bash
echo "🔄 Rolling back changes..."

# Revert to last known good state
git revert --no-commit HEAD~$1

# Run immediate tests
npm run test:critical

# Deploy if tests pass
if [ $? -eq 0 ]; then
  echo "✅ Rollback successful"
  npm run deploy
else
  echo "❌ Rollback tests failed"
  exit 1
fi
```

#### 3. **Monitoring and Alerts**
```typescript
// lib/monitoring.ts
export class CleanupMonitoring {
  static trackMigration(phase: string, action: string) {
    // Track each migration step
    analytics.track('cleanup_migration', {
      phase,
      action,
      timestamp: Date.now()
    });
  }

  static alertOnRegression(metric: string, value: number, threshold: number) {
    if (value > threshold) {
      // Send alert to team
      sendAlert(`Performance regression detected: ${metric} = ${value} (threshold: ${threshold})`);
    }
  }
}
```

## Success Criteria

### Testing Coverage Targets
- **Unit Test Coverage**: 80%+ for all new/modified components
- **Integration Test Coverage**: 100% for critical user workflows
- **E2E Test Coverage**: 100% for main application features
- **Performance Tests**: No degradation >10% in key metrics

### Quality Gates
- [ ] All existing functionality preserved
- [ ] No new bugs introduced during cleanup
- [ ] Performance maintained or improved
- [ ] User workflows remain unchanged
- [ ] API contracts preserved

This comprehensive testing strategy ensures that the massive codebase cleanup can proceed safely without breaking existing functionality, while building the foundation for a more maintainable and testable codebase.