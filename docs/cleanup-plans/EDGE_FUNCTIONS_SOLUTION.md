# Edge Functions Crisis Solution

## The Problem
- App calls **25 different Edge Functions** across 37+ files
- Only **1 function exists locally** (`ai-command-processor`)
- **24 missing functions** will cause widespread app failures during cleanup
- `supabase functions download` failed for all 37 remote functions

## The Solution: Hybrid Function Handler

Create a smart function router that handles both local and remote functions automatically.

### 1. Create Hybrid Supabase Client

```typescript
// src/lib/supabase-hybrid.ts
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'http://127.0.0.1:54321';
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

// Create clients for both local and remote
const localClient = createClient(
  'http://127.0.0.1:54321',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'
);

const remoteClient = createClient(
  'https://pwaeknalhosfwuxkpaet.supabase.co',
  import.meta.env.VITE_SUPABASE_ANON_KEY_REMOTE || 'your-remote-anon-key'
);

// Functions that exist locally
const LOCAL_FUNCTIONS = [
  'ai-command-processor'
];

// Smart function router
export const hybridSupabase = {
  ...localClient,
  functions: {
    invoke: async (functionName: string, options?: any) => {
      console.log(`🔄 Function call: ${functionName}`);
      
      if (LOCAL_FUNCTIONS.includes(functionName)) {
        console.log(`📍 Using LOCAL function: ${functionName}`);
        return localClient.functions.invoke(functionName, options);
      } else {
        console.log(`🌐 Using REMOTE function: ${functionName}`);
        try {
          return await remoteClient.functions.invoke(functionName, options);
        } catch (error) {
          console.error(`❌ Remote function ${functionName} failed:`, error);
          // Optionally return mock response or handle gracefully
          return { 
            data: null, 
            error: { message: `Function ${functionName} not available in development` }
          };
        }
      }
    }
  }
};
```

### 2. Update Main Supabase Client

```typescript
// src/integrations/supabase/client.ts
import { hybridSupabase } from '@/lib/supabase-hybrid';

// Replace existing export with hybrid client
export const supabase = hybridSupabase;
```

### 3. Environment Configuration

```bash
# .env.local - Hybrid setup
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
VITE_SUPABASE_ANON_KEY_REMOTE=your-actual-remote-anon-key
```

### 4. Function Usage Monitoring

```typescript
// src/utils/function-monitor.ts
export const functionUsageLog: Record<string, number> = {};

export const logFunctionUsage = (functionName: string, success: boolean) => {
  const key = `${functionName}_${success ? 'success' : 'failure'}`;
  functionUsageLog[key] = (functionUsageLog[key] || 0) + 1;
  
  if (import.meta.env.DEV) {
    console.log(`📊 Function usage: ${functionName} - ${success ? '✅' : '❌'}`);
  }
};

export const getFunctionUsageReport = () => {
  return Object.entries(functionUsageLog)
    .map(([key, count]) => `${key}: ${count}`)
    .join('\n');
};
```

## Benefits of This Approach

### ✅ Immediate Benefits
- **No app crashes** - All function calls handled gracefully
- **Real functionality** - Remote functions work as expected
- **Local development** - ai-command-processor works locally
- **Safe experimentation** - Can modify local functions without risk

### ✅ Cleanup Safety
- **Zero downtime** - App continues working during cleanup
- **Function monitoring** - Track which functions are called
- **Gradual migration** - Can move functions to local as needed
- **Error handling** - Graceful degradation for missing functions

### ✅ Development Workflow
- **Database isolation** - Still using local Supabase for data
- **Function flexibility** - Mix of local and remote functions
- **Easy debugging** - Clear logging of function routing
- **Production safety** - No risk to production functions

## Implementation Steps

### Phase 0A: Immediate Implementation (Day 1)
```bash
# 1. Create hybrid client
# Copy code above to src/lib/supabase-hybrid.ts

# 2. Update main client
# Modify src/integrations/supabase/client.ts

# 3. Test hybrid setup
npm run dev
# Verify: Login works, functions don't crash app

# 4. Monitor function usage
# Check console for function routing logs
```

### Phase 0B: Verification (Day 2)
```bash
# 1. Test critical workflows
./scripts/verify-critical-workflows.sh

# 2. Test function-heavy features
# - Admin panel (uses admin-* functions)
# - Email sending (uses send-email)
# - Team invitations (uses create-team-invitation)

# 3. Document any failures
./scripts/audit-edge-functions.sh
```

## Alternative Approaches

### Option B: Mock Missing Functions (Not Recommended)
```typescript
// Quick mock for testing only
const mockFunctions = {
  'send-email': () => ({ data: { success: true }, error: null }),
  'admin-backup-database': () => ({ data: { message: 'Mock backup' }, error: null }),
  // ... other mocks
};
```

**Why not recommended**: Hides real issues, may cause production problems.

### Option C: Recreate Functions Locally (Time-Intensive)
Manually recreate critical functions by examining production code.

**Why not recommended**: Very time-intensive, may introduce bugs.

## Conclusion

The **Hybrid Function Handler** approach provides:
- ✅ **Immediate safety** - No app crashes during cleanup
- ✅ **Real functionality** - Remote functions continue working
- ✅ **Local development** - Database isolation preserved
- ✅ **Easy implementation** - Simple code changes
- ✅ **Future flexibility** - Can migrate functions gradually

This solves the Edge Functions crisis while maintaining the excellent local Supabase setup for safe cleanup operations.