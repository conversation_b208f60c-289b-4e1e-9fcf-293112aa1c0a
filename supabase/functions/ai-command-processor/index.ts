import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { GoogleGenerativeAI } from "https://esm.sh/@google/generative-ai@0.2.1";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";
import { generateIntelligentSuggestions, findSimilarItems, suggestProperties } from "./intelligentSuggestions.ts";

const GEMINI_API_KEY = Deno.env.get("GEMINI_API_KEY") || "AIzaSyD55Kn_94EdiW7czvu8qZ4G6R76vRL563s";
const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_ANON_KEY = Deno.env.get("SUPABASE_ANON_KEY") || "";
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }
  try {
    if (!GEMINI_API_KEY) {
      throw new Error("GEMINI_API_KEY is not set");
    }
    if (!SUPABASE_URL || !SUPABASE_ANON_KEY || !SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase environment variables are not set");
    }
    const requestBody = await req.json();
    console.log("Request body:", requestBody);
    const { command } = requestBody;
    if (!command || command.trim() === "") {
      throw new Error("Command is required");
    }

    // Extract user ID from JWT token for security
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      throw new Error("Authorization header is required");
    }

    const token = authHeader.replace('Bearer ', '');
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

    // Verify the JWT token and get user ID
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    if (authError || !user) {
      throw new Error("Invalid or expired token");
    }

    const userId = user.id;
    console.log("Authenticated user ID:", userId);

    // Get user's properties for context
    const { data: userProperties, error: propertiesError } = await supabase
      .from("properties")
      .select(`
        id, name, address,
        team_properties!inner(
          team_id,
          team_members!inner(
            user_id,
            status
          )
        )
      `)
      .eq("team_properties.team_members.user_id", userId)
      .eq("team_properties.team_members.status", "active");

    if (propertiesError) {
      console.error("Error fetching user properties:", propertiesError);
    }

    console.log("User properties:", userProperties);

    // Get user's inventory items for context
    const { data: inventoryItems, error: inventoryError } = await supabase.rpc('get_user_inventory_items', {
      user_id_param: userId
    });

    if (inventoryError) {
      console.error("Error fetching inventory items:", inventoryError);
    }

    // Format inventory items for AI context
    const formattedInventoryItems = inventoryItems?.map(item => ({
      id: item.id,
      name: item.name,
      quantity: item.quantity,
      min_quantity: item.min_quantity,
      property_name: item.property_name,
      collection_name: item.collection_name,
      price: item.price,
      image_url: item.image_url
    })) || [];

    console.log("User inventory items:", formattedInventoryItems);

    // Initialize Gemini AI
    const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

    // Create context-aware prompt
    const propertiesContext = userProperties?.length > 0
      ? `Available properties: ${userProperties.map(p => `"${p.name}" (${p.address})`).join(", ")}`
      : "No properties available";

    const inventoryContext = formattedInventoryItems?.length > 0
      ? `Current inventory: ${formattedInventoryItems.map(item => `${item.name} (${item.quantity} units at ${item.property_name})`).slice(0, 20).join(", ")}`
      : "No inventory items available";

    const prompt = `
    You are an AI assistant for a property management system. Analyze the user's command and determine what action they want to take.

    User's context:
    ${propertiesContext}
    ${inventoryContext}

    User command: "${command}"

    The system supports the following actions:
    1. Add a property (needs name, address, city, state, zip, and optionally bedrooms and bathrooms)
    2. Create a collection (needs name and optionally budget)
    3. Add a maintenance task (needs title, property, and optionally severity, description, due date, assignee)
    4. Add inventory items (needs name, quantity, property, and optionally collection, min_quantity, price, image_url) - ONLY use this if the item doesn't already exist
    5. Update inventory item (needs item name or id, and the new quantity and/or min_quantity) - ALWAYS use this if a similar item already exists
    6. Create purchase order (needs items to include, can be "all low stock items" or specific items by name)

    IMPORTANT RULES:
    - For inventory: If an item already exists in the user's inventory, use "updateInventoryItem" instead of "addInventoryItem"
    - For properties: Use partial matching for property names (e.g., "Beach" should match "Beach House", "True" should match "20235 True Rd (Andy)")
    - For maintenance tasks: Extract assignee names carefully and include them in the assignee field
    - For purchase orders: Support both specific items and "all low stock items"

    EXAMPLES OF MAINTENANCE TASK CREATION:
    - "Fix the broken window at Beach House" → addMaintenanceTask with title="Fix broken window", property="Beach House", severity="medium"
    - "Replace the light bulb in the kitchen at Ocean View, make it high priority" → addMaintenanceTask with title="Replace light bulb", property="Ocean View", severity="high"
    - "Clean the pool at the downtown apartment, due next Friday" → addMaintenanceTask with title="Clean pool", property="downtown apartment", dueDate="next Friday"
    - "There is a broken window at Thames, assign to Ashlee Glass and make it due on March 23rd" → addMaintenanceTask with title="Fix broken window", property="Thames", assignee="Ashlee Glass", dueDate="March 23rd"

    EXAMPLE JSON RESPONSE for "Fix the door, assign to Ashlee Glass":
    {
      "action": "addMaintenanceTask",
      "data": {
        "title": "Fix door",
        "assignee": "Ashlee Glass"
      },
      "message": "I'll create a maintenance task to fix the door and assign it to Ashlee Glass."
    }

    EXAMPLE JSON RESPONSE for "add 3 towels to Beach House":
    {
      "action": "addInventoryItem",
      "data": {
        "name": "towels",
        "quantity": 3,
        "property": "Beach House",
        "collection": "General"
      },
      "message": "I'll add 3 towels to Beach House inventory."
    }

    EXAMPLE JSON RESPONSE for "add 1 fly swatter to Thames inventory":
    {
      "action": "addInventoryItem",
      "data": {
        "name": "fly swatter",
        "quantity": 1,
        "property": "Thames",
        "collection": "General"
      },
      "message": "I'll add 1 fly swatter to Thames inventory."
    }

    EXAMPLE JSON RESPONSE for "we need 1 fly swatter at True" (where "True" should match "20235 True Rd (Andy)"):
    {
      "action": "addInventoryItem",
      "data": {
        "name": "fly swatter",
        "quantity": 1,
        "property": "20235 True Rd (Andy)",
        "collection": "General"
      },
      "message": "I'll add 1 fly swatter to 20235 True Rd (Andy) property."
    }

    EXAMPLE JSON RESPONSE for "Create a purchase order for all low stock items":
    {
      "action": "createPurchaseOrder",
      "data": {
        "allLowStock": true
      },
      "message": "I'll create a purchase order for all low stock items."
    }

    EXAMPLE JSON RESPONSE for "Create a purchase order for towels and toilet paper":
    {
      "action": "createPurchaseOrder",
      "data": {
        "items": ["towels", "toilet paper"]
      },
      "message": "I'll create a purchase order for towels and toilet paper."
    }

    ANOTHER EXAMPLE for "broken door handle at c st, assign to Ashlee glass":
    {
      "action": "addMaintenanceTask",
      "data": {
        "title": "Broken door handle",
        "property": "c st",
        "assignee": "Ashlee glass"
      },
      "message": "Successfully added maintenance task 'Broken door handle' for c st and assigned to Ashlee glass."
    }
    - "Repair the leaky faucet, assign it to John Smith" → addMaintenanceTask with title="Repair leaky faucet", assignee="John Smith"
    - "Schedule HVAC maintenance for next week, assign to Mike Johnson" → addMaintenanceTask with title="HVAC maintenance", assignee="Mike Johnson", dueDate="next week"
    - "Fix the broken door handle, assign to maintenance team" → addMaintenanceTask with title="Fix broken door handle", assignee="maintenance team"

    Based on the user's command, determine what action they want to take and extract the necessary information.

    Respond with a JSON object that contains:
    - action: The action to take (addProperty, createCollection, addMaintenanceTask, addInventoryItem, updateInventoryItem, createPurchaseOrder)
    - data: An object containing the extracted information needed for the action
    - message: A user-friendly message describing what will be done

    For addMaintenanceTask, the data object MUST include these fields:
    - title: The task title (required)
    - description: Task description (optional)
    - property: Property name (optional)
    - severity: "low", "medium", "high", or "critical" (optional, defaults to "medium")
    - dueDate: Due date if mentioned (optional)
    - assignee: Name of person to assign to if mentioned (IMPORTANT: always include this field, set to null if no assignee mentioned)

    For addInventoryItem, the data object should include:
    - name: Item name (required)
    - quantity: Number of items (required)
    - property: Property name (required)
    - collection: Collection name (optional, defaults to "General")
    - min_quantity: Minimum quantity threshold (optional)
    - price: Item price (optional)
    - image_url: Item image URL (optional)

    For updateInventoryItem, the data object should include:
    - name: Item name or ID (required)
    - quantity: New quantity (optional)
    - min_quantity: New minimum quantity (optional)
    - property: Property name (optional, for context)

    Only return the JSON object, nothing else.
    `;
    // Call the Gemini API
    console.log("Sending prompt to Gemini API");
    const result = await model.generateContent(prompt);
    const response = result.response;
    const textResponse = response.text();
    console.log("Gemini API response:", textResponse);
    // Parse the AI response
    try {
      // Extract JSON if it's within markdown code blocks
      const jsonMatch = textResponse.match(/```(?:json)?\s*([\s\S]*?)\s*```/) || textResponse.match(/\{[\s\S]*\}/);
      const jsonText = jsonMatch ? jsonMatch[1] || jsonMatch[0] : textResponse;
      const parsedResponse = JSON.parse(jsonText.trim());
      console.log("DEBUG: AI Parsed response:", JSON.stringify(parsedResponse, null, 2));
      // Post-process to extract assignee if AI missed it
      if (parsedResponse.action === "addMaintenanceTask" && !parsedResponse.data.assignee) {
        const assigneeMatch = command.match(/assign\s+(?:to|it\s+to)\s+([^,\s]+(?:\s+[^,\s]+)*)/i);
        if (assigneeMatch) {
          parsedResponse.data.assignee = assigneeMatch[1].trim();
          console.log("DEBUG: Post-processed assignee extraction:", parsedResponse.data.assignee);
        }
      }
      // Process the AI's suggested action
      let processingResult;
      switch(parsedResponse.action){
        case "addProperty":
          processingResult = await handleAddProperty(supabase, userId, parsedResponse.data);
          break;
        case "createCollection":
          processingResult = await handleCreateCollection(supabase, userId, parsedResponse.data);
          break;
        case "addMaintenanceTask":
          processingResult = await handleAddMaintenanceTask(supabase, userId, parsedResponse.data);
          break;
        case "addInventoryItem":
          processingResult = await handleSmartInventoryAdd(supabase, userId, parsedResponse.data);
          break;
        case "updateInventoryItem":
          processingResult = await handleUpdateInventoryItem(supabase, userId, parsedResponse.data);
          break;
        case "createPurchaseOrder":
          processingResult = await handleCreatePurchaseOrder(supabase, userId, parsedResponse.data);
          break;
        default:
          // Generate intelligent suggestions based on the original command
          const intelligentSuggestion = await generateIntelligentSuggestions(
            command,
            supabase,
            userId,
            {
              properties: userProperties,
              inventoryItems: formattedInventoryItems
            }
          );

          processingResult = {
            success: false,
            message: intelligentSuggestion.message,
            suggestions: intelligentSuggestion.suggestions,
            category: intelligentSuggestion.category,
            intent: intelligentSuggestion.intent
          };
      }
      return new Response(JSON.stringify(processingResult), {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders
        }
      });
    } catch (parseError) {
      console.error("Error parsing AI response:", parseError);
      console.error("Raw AI response:", textResponse);
      throw new Error(`Failed to parse AI response: ${parseError.message}`);
    }
  } catch (error) {
    console.error("Error in AI command processor:", error);
    return new Response(JSON.stringify({
      success: false,
      message: error.message || "An unexpected error occurred",
      error: error.message
    }), {
      status: 500,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders
      }
    });
  }
});

// Handler functions
async function handleAddProperty(supabase, userId, data) {
  try {
    // Validate required fields
    if (!data.name || !data.address || !data.city || !data.state) {
      return {
        success: false,
        message: "Property name, address, city, and state are required."
      };
    }

    // Insert the new property
    const { data: newProperty, error } = await supabase.from("properties").insert({
      user_id: userId,
      name: data.name,
      address: data.address,
      city: data.city,
      state: data.state,
      zip: data.zip || "",
      bedrooms: data.bedrooms || 1,
      bathrooms: data.bathrooms || 1
    }).select().single();
    if (error) {
      console.error("Error adding property:", error);
      return {
        success: false,
        message: `Failed to add property: ${error.message}`
      };
    }
    return {
      success: true,
      message: `Successfully added property "${data.name}"`,
      action: "addProperty",
      entityType: "property",
      entityId: newProperty.id
    };
  } catch (error) {
    console.error("Error in handleAddProperty:", error);
    return {
      success: false,
      message: `Error adding property: ${error.message}`
    };
  }
}

async function handleCreateCollection(supabase, userId, data) {
  try {
    // Validate required fields
    if (!data.name) {
      return {
        success: false,
        message: "Collection name is required."
      };
    }
    // Insert the new collection
    const { data: newCollection, error } = await supabase.from("collections").insert({
      user_id: userId,
      name: data.name,
      budget: data.budget || null
    }).select().single();
    if (error) {
      console.error("Error creating collection:", error);
      return {
        success: false,
        message: `Failed to create collection: ${error.message}`
      };
    }
    let additionalMessage = "";
    if (data.budget) {
      additionalMessage = ` with budget $${data.budget}`;
    }
    return {
      success: true,
      message: `Successfully created collection "${data.name}"${additionalMessage}`,
      action: "createCollection",
      entityType: "collection",
      entityId: newCollection.id
    };
  } catch (error) {
    console.error("Error in handleCreateCollection:", error);
    return {
      success: false,
      message: `Error creating collection: ${error.message}`
    };
  }
}

// FIXED VERSION: Added team_id support for service providers
async function handleAddMaintenanceTask(supabase, userId, data) {
  try {
    // Validate required fields
    if (!data.title) {
      return {
        success: false,
        message: "Maintenance task title is required."
      };
    }

    // Get property ID if property name was provided
    let propertyId = null;
    let propertyName = null;
    let teamId = null;
    
    if (data.property) {
      // Use RPC function for better property access control
      const { data: propertyData, error: propertyError } = await supabase.rpc('get_user_property_by_name', {
        p_user_id: userId,
        p_property_name: data.property
      });
      
      if (propertyError || !propertyData || propertyData.length === 0) {
        console.warn("Property not found:", data.property);
        // Get all properties for intelligent suggestions
        const { data: allProperties } = await supabase
          .from("properties")
          .select(`
            name,
            team_properties!inner(
              team_id,
              team_members!inner(
                user_id,
                status
              )
            )
          `)
          .eq("team_properties.team_members.user_id", userId)
          .eq("team_properties.team_members.status", "active");

        if (allProperties && allProperties.length > 0) {
          const propertySuggestions = suggestProperties(allProperties);
          return {
            success: false,
            message: "I need to know which property this maintenance task is for. Which property should I assign this to?",
            suggestions: [
              ...propertySuggestions.map(name => `Try: "${data.title} at ${name}"`),
              "Be specific: 'Create maintenance task for [description] at [property name]'",
              "Example: 'Fix the broken sink at Beach House'"
            ],
            category: 'maintenance',
            intent: 'maintenance_property_required'
          };
        } else {
          return {
            success: false,
            message: "No properties found. Please add a property first before creating maintenance tasks.",
            suggestions: [
              "Add a property first: 'Add property [name] at [address]'",
              "Example: 'Add property Beach House at 123 Ocean Drive'"
            ],
            category: 'maintenance',
            intent: 'no_properties_available'
          };
        }
      } else {
        propertyId = propertyData[0].id;
        propertyName = propertyData[0].name;
        
        // Get team_id for this property and user
        const { data: teamPropertyData, error: teamPropertyError } = await supabase
          .from('team_properties')
          .select('team_id, team_members!inner(user_id, status)')
          .eq('property_id', propertyId)
          .eq('team_members.user_id', userId)
          .eq('team_members.status', 'active')
          .limit(1);
          
        if (!teamPropertyError && teamPropertyData && teamPropertyData.length > 0) {
          teamId = teamPropertyData[0].team_id;
          console.log(`[handleAddMaintenanceTask] Found team_id ${teamId} for property ${propertyId}`);
        }
      }
    }

    // Get assignee info if assignee name was provided
    let assigneeId = null;
    let assigneeName: string | null = null;
    let providerId = null;
    console.log("DEBUG: Maintenance task data received:", JSON.stringify(data, null, 2));
    
    if (data.assignee) {
      console.log("DEBUG: Looking for assignee:", data.assignee);
      // Try to find assignee by name (first name, last name, or full name)
      const { data: assigneeData, error: assigneeError } = await supabase
        .from("profiles")
        .select("id, first_name, last_name, email, role")
        .or(`first_name.ilike.%${data.assignee}%,last_name.ilike.%${data.assignee}%,email.ilike.%${data.assignee}%`)
        .limit(5);
        
      if (assigneeError) {
        console.warn("Error finding assignee:", assigneeError);
      } else if (assigneeData && assigneeData.length > 0) {
        const assignee = assigneeData[0];
        assigneeId = assignee.id;
        assigneeName = `${assignee.first_name || ''} ${assignee.last_name || ''}`.trim() || assignee.email;
        
        // If assignee is a service provider, set provider_id
        if (assignee.role === 'service_provider') {
          providerId = assignee.id;
        }
        
        console.log("DEBUG: Found assignee:", { assigneeId, assigneeName, providerId });
      } else {
        // If no user found, just use the assignee name as provided
        assigneeName = data.assignee;
        console.log("DEBUG: No user found for assignee, using name as provided:", assigneeName);
      }
    }

    // Insert the new maintenance task with team_id
    const { data: newTask, error } = await supabase.from("maintenance_tasks").insert({
      user_id: userId,
      title: data.title,
      description: data.description || "",
      property_id: propertyId,
      property_name: propertyName || "General",
      severity: data.severity || "medium",
      status: "new",
      due_date: data.dueDate || null,
      assigned_to: assigneeName,
      provider_id: providerId,
      team_id: teamId  // This was missing in the original code!
    }).select().single();

    if (error) {
      console.error("Error adding maintenance task:", error);
      return {
        success: false,
        message: `Failed to add maintenance task: ${error.message}`
      };
    }

    // Build success message with assignee info
    let successMessage = `Successfully added maintenance task "${data.title}" for ${propertyName || "General"}`;
    if (assigneeName) {
      successMessage += ` and assigned to ${assigneeName}`;
    }
    if (data.dueDate) {
      successMessage += ` (due: ${data.dueDate})`;
    }

    return {
      success: true,
      message: successMessage,
      action: "addMaintenanceTask",
      entityType: "maintenance_task",
      entityId: newTask.id
    };
  } catch (error) {
    console.error("Error in handleAddMaintenanceTask:", error);
    return {
      success: false,
      message: `Error adding maintenance task: ${error.message}`
    };
  }
}

// Smart inventory handler that decides whether to add, update, or copy
async function handleSmartInventoryAdd(supabase, userId, data) {
  try {
    // Validate required fields
    if (!data.name) {
      return {
        success: false,
        message: "Item name is required. Please specify what item you want to add.",
        suggestions: [
          "Try: 'Add [quantity] [item name] to [property name]'",
          "Example: 'Add 5 towels to Beach House'",
          "Be specific: 'Add 3 bath towels to Ocean View'"
        ],
        category: 'inventory',
        intent: 'missing_item_name'
      };
    }

    if (!data.quantity || data.quantity <= 0) {
      return {
        success: false,
        message: "Please specify how many items you want to add.",
        suggestions: [
          "Try: 'Add [quantity] [item name] to [property name]'",
          "Example: 'Add 5 towels to Beach House'",
          "Specify the quantity: 'Add 3 bath towels to Ocean View'"
        ],
        category: 'inventory',
        intent: 'missing_quantity'
      };
    }

    // Get property ID if property name was provided
    let propertyId = null;
    let propertyName = null;

    if (data.property) {
      // Use RPC function for better property access control
      const { data: propertyData, error: propertyError } = await supabase.rpc('get_user_property_by_name', {
        p_user_id: userId,
        p_property_name: data.property
      });

      if (propertyError || !propertyData || propertyData.length === 0) {
        console.warn("Property not found:", data.property);
        // Get all properties for intelligent suggestions
        const { data: allProperties } = await supabase
          .from("properties")
          .select(`
            name,
            team_properties!inner(
              team_id,
              team_members!inner(
                user_id,
                status
              )
            )
          `)
          .eq("team_properties.team_members.user_id", userId)
          .eq("team_properties.team_members.status", "active");

        if (allProperties && allProperties.length > 0) {
          const propertySuggestions = suggestProperties(allProperties);
          return {
            success: false,
            message: "I need to know which property to add this item to. Which property should I use?",
            suggestions: [
              ...propertySuggestions.map(name => `Try: "Add ${data.quantity} ${data.name} to ${name}"`),
              "Be specific: 'Add items to [exact property name]'",
              "Example: 'Add 5 towels to Beach House'"
            ],
            category: 'inventory',
            intent: 'inventory_property_required'
          };
        } else {
          return {
            success: false,
            message: "No properties found. Please add a property first before adding inventory items.",
            suggestions: [
              "Add a property first: 'Add property [name] at [address]'",
              "Example: 'Add property Beach House at 123 Ocean Drive'"
            ],
            category: 'inventory',
            intent: 'no_properties_available'
          };
        }
      } else {
        propertyId = propertyData[0].id;
        propertyName = propertyData[0].name;
      }
    }

    if (!propertyId) {
      return {
        success: false,
        message: "Please specify which property to add the item to.",
        suggestions: [
          "Try: 'Add [quantity] [item name] to [property name]'",
          "Example: 'Add 5 towels to Beach House'",
          "Specify the property: 'Add items to Ocean View'"
        ],
        category: 'inventory',
        intent: 'missing_property'
      };
    }

    // Check if item already exists in the target property
    const { data: existingItems } = await supabase
      .from("inventory_items")
      .select("*")
      .eq("user_id", userId)
      .eq("property_id", propertyId)
      .ilike("name", `%${data.name}%`);

    // If item exists in this property, update it instead
    if (existingItems && existingItems.length > 0) {
      const existingItem = existingItems[0];
      const newQuantity = existingItem.quantity + data.quantity;
      return await handleUpdateInventoryItem(supabase, userId, {
        id: existingItem.id,
        quantity: newQuantity,
        property: propertyName
      });
    }

    // Check if similar item exists in other properties
    const { data: similarItems } = await supabase
      .from("inventory_items")
      .select("*")
      .eq("user_id", userId)
      .neq("property_id", propertyId)
      .ilike("name", `%${data.name}%`)
      .limit(1);

    // If similar item exists in other properties, offer to copy it
    if (similarItems && similarItems.length > 0) {
      const sourceItem = similarItems[0];
      return await copyInventoryItemToProperty(supabase, userId, sourceItem, propertyId, propertyName, data.quantity || 1);
    }

    // Item doesn't exist anywhere, create new one
    const itemData = {
      ...data,
      property: propertyName,
      propertyId: propertyId
    };
    return await handleAddInventoryItem(supabase, userId, itemData);

  } catch (error) {
    console.error("Error in handleSmartInventoryAdd:", error);
    return {
      success: false,
      message: `Error processing inventory request: ${error.message}`
    };
  }
}

// Copy inventory item to a different property
async function copyInventoryItemToProperty(supabase, userId, sourceItem, targetPropertyId, targetPropertyName, quantity) {
  try {
    const { data: newItem, error } = await supabase.from("inventory_items").insert({
      user_id: userId,
      property_id: targetPropertyId,
      collection_id: sourceItem.collection_id,
      name: sourceItem.name,
      description: sourceItem.description || "",
      quantity: quantity,
      min_quantity: sourceItem.min_quantity || 1,
      price: sourceItem.price || null,
      image_url: sourceItem.image_url || null
    }).select().single();

    if (error) {
      console.error("Error copying inventory item:", error);
      return {
        success: false,
        message: `Failed to copy item: ${error.message}`
      };
    }

    return {
      success: true,
      message: `Successfully added ${quantity} ${sourceItem.name} to ${targetPropertyName} (copied from existing item)`,
      action: "addInventoryItem",
      entityType: "inventory_item",
      entityId: newItem.id
    };
  } catch (error) {
    console.error("Error in copyInventoryItemToProperty:", error);
    return {
      success: false,
      message: `Error copying item: ${error.message}`
    };
  }
}

async function handleAddInventoryItem(supabase, userId, data) {
  try {
    // Validate required fields
    if (!data.name) {
      return {
        success: false,
        message: "Item name is required."
      };
    }

    if (!data.quantity || data.quantity <= 0) {
      return {
        success: false,
        message: "Quantity must be greater than 0."
      };
    }

    // Get property ID if property name was provided
    let propertyId = null;
    if (data.property) {
      console.log(`[handleAddInventoryItem] Looking for property: "${data.property}" for user: ${userId}`);

      // Use RPC function for better property access control
      const { data: propertyData, error: propertyError } = await supabase.rpc('get_user_property_by_name', {
        p_user_id: userId,
        p_property_name: data.property
      });

      console.log(`[handleAddInventoryItem] RPC result for property "${data.property}":`, propertyData);
      console.log(`[handleAddInventoryItem] RPC error:`, propertyError);

      if (propertyError || !propertyData || propertyData.length === 0) {
        console.warn("Property not found:", data.property);
        return {
          success: false,
          message: `Property "${data.property}" not found. Please check the property name and try again.`
        };
      } else {
        propertyId = propertyData[0].id;
        console.log(`[handleAddInventoryItem] Found property ID: ${propertyId} for property: ${data.property}`);
      }
    }

    // Get collection ID if collection name was provided
    let collectionId = null;
    if (data.collection) {
      const { data: collectionData, error: collectionError } = await supabase
        .from("collections")
        .select("id")
        .eq("user_id", userId)
        .ilike("name", `%${data.collection}%`)
        .limit(1)
        .single();

      if (collectionError) {
        console.warn("Collection not found, will create default:", data.collection);
      } else {
        collectionId = collectionData.id;
      }
    }

    // If no collection found, create or use default "General" collection
    if (!collectionId) {
      const { data: defaultCollection, error: defaultError } = await supabase
        .from("collections")
        .select("id")
        .eq("user_id", userId)
        .eq("name", "General")
        .limit(1)
        .single();

      if (defaultError) {
        // Create default collection
        const { data: newCollection, error: createError } = await supabase
          .from("collections")
          .insert({
            user_id: userId,
            name: "General"
          })
          .select()
          .single();

        if (createError) {
          console.error("Error creating default collection:", createError);
        } else {
          collectionId = newCollection.id;
        }
      } else {
        collectionId = defaultCollection.id;
      }
    }

    // Insert the new inventory item
    const { data: newItem, error } = await supabase.from("inventory_items").insert({
      user_id: userId,
      property_id: propertyId,
      collection_id: collectionId,
      name: data.name,
      description: data.description || "",
      quantity: data.quantity,
      min_quantity: data.min_quantity || 1,
      price: data.price || null,
      image_url: data.image_url || null
    }).select().single();

    if (error) {
      console.error("Error adding inventory item:", error);
      return {
        success: false,
        message: `Failed to add inventory item: ${error.message}`
      };
    }

    let locationMessage = "";
    if (data.property) {
      locationMessage = ` to ${data.property}`;
    }

    return {
      success: true,
      message: `Successfully added ${data.quantity} ${data.name}${locationMessage}`,
      action: "addInventoryItem",
      entityType: "inventory_item",
      entityId: newItem.id
    };
  } catch (error) {
    console.error("Error in handleAddInventoryItem:", error);
    return {
      success: false,
      message: `Error adding inventory item: ${error.message}`
    };
  }
}

async function handleUpdateInventoryItem(supabase, userId, data) {
  try {
    // Validate required fields
    if (!data.name && !data.id) {
      return {
        success: false,
        message: "Item name or ID is required to update inventory."
      };
    }

    // Find the item to update
    let query = supabase.from("inventory_items").select("*").eq("user_id", userId);

    if (data.id) {
      query = query.eq("id", data.id);
    } else {
      query = query.ilike("name", `%${data.name}%`);
    }

    const { data: items, error: findError } = await query.limit(5);

    if (findError) {
      console.error("Error finding inventory item:", findError);
      return {
        success: false,
        message: `Error finding inventory item: ${findError.message}`
      };
    }

    if (!items || items.length === 0) {
      return {
        success: false,
        message: `No inventory item found matching "${data.name || data.id}". Please check the item name and try again.`
      };
    }

    // If multiple items found, try to narrow down by property
    let targetItem = items[0];
    if (items.length > 1 && data.property) {
      const propertyMatch = items.find(item =>
        item.property_name && item.property_name.toLowerCase().includes(data.property.toLowerCase())
      );
      if (propertyMatch) {
        targetItem = propertyMatch;
      }
    }

    // Prepare update data
    const updateData = {};
    if (data.quantity !== undefined) {
      updateData.quantity = data.quantity;
    }
    if (data.min_quantity !== undefined) {
      updateData.min_quantity = data.min_quantity;
    }

    if (Object.keys(updateData).length === 0) {
      return {
        success: false,
        message: "No valid fields to update. Please specify quantity or min_quantity."
      };
    }

    // Update the item
    const { data: updatedItem, error: updateError } = await supabase
      .from("inventory_items")
      .update(updateData)
      .eq("id", targetItem.id)
      .eq("user_id", userId)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating inventory item:", updateError);
      return {
        success: false,
        message: `Failed to update inventory item: ${updateError.message}`
      };
    }

    let updateMessage = `Successfully updated ${targetItem.name}`;
    if (data.quantity !== undefined) {
      updateMessage += ` quantity to ${data.quantity}`;
    }
    if (data.min_quantity !== undefined) {
      updateMessage += ` minimum quantity to ${data.min_quantity}`;
    }

    return {
      success: true,
      message: updateMessage,
      action: "updateInventoryItem",
      entityType: "inventory_item",
      entityId: updatedItem.id
    };
  } catch (error) {
    console.error("Error in handleUpdateInventoryItem:", error);
    return {
      success: false,
      message: `Error updating inventory item: ${error.message}`
    };
  }
}

async function handleCreatePurchaseOrder(supabase, userId, data) {
  try {
    // Handle "all low stock items" case
    if (data.allLowStock) {
      // Get property ID if specified
      let propertyId = null;
      if (data.property) {
        const { data: properties, error: propError } = await supabase
          .from("properties")
          .select(`
            id, name,
            team_properties!inner(
              team_id,
              team_members!inner(
                user_id,
                status
              )
            )
          `)
          .eq("team_properties.team_members.user_id", userId)
          .eq("team_properties.team_members.status", "active")
          .ilike("name", `%${data.property}%`)
          .limit(1);
        if (propError) {
          console.error("Error finding property:", propError);
        } else if (properties && properties.length > 0) {
          propertyId = properties[0].id;
        }
      }
      // Use RPC function to get low stock items
      const { data: lowStockItems, error: itemsError } = await supabase.rpc('get_low_stock_items', {
        user_id_param: userId,
        property_id_param: propertyId
      });
      if (itemsError) {
        console.error("Error fetching low stock items:", itemsError);
        return {
          success: false,
          message: `Error fetching low stock items: ${itemsError.message}`
        };
      }
      if (!lowStockItems || lowStockItems.length === 0) {
        return {
          success: false,
          message: "No low stock items found. All inventory levels are adequate."
        };
      }
      // Group items by property
      const itemsByProperty = {};
      lowStockItems.forEach(item => {
        const propId = item.property_id || 'general';
        if (!itemsByProperty[propId]) {
          itemsByProperty[propId] = [];
        }
        itemsByProperty[propId].push({
          inventory_item_id: item.id,
          name: item.name,
          quantity: Math.max(1, (item.min_quantity || 1) - item.quantity),
          price: item.price || 0
        });
      });
      const createdOrders: Array<{ id: any; propertyName: any; itemCount: number }> = [];
      // Create purchase orders for each property
      for (const [propertyId, items] of Object.entries(itemsByProperty)){
        const typedItems = items as any[];
        // Calculate total price
        const totalPrice = typedItems.reduce((sum, item)=>sum + (item.price || 0) * item.quantity, 0);
        // Create the purchase order
        const { data: order, error: orderError } = await supabase.from("purchase_orders").insert({
          user_id: userId,
          property_id: propertyId === 'general' ? null : propertyId,
          status: "pending",
          total_price: totalPrice > 0 ? totalPrice : null,
          notes: data.notes || ""
        }).select().single();
        if (orderError) {
          console.error("Error creating purchase order:", orderError);
          continue;
        }
        // Add items to the purchase order
        const orderItems = typedItems.map(item => ({
          purchase_order_id: order.id,
          inventory_item_id: item.inventory_item_id,
          quantity: item.quantity,
          unit_price: item.price || 0
        }));
        const { error: itemsInsertError } = await supabase.from("purchase_order_items").insert(orderItems);
        if (itemsInsertError) {
          console.error("Error adding items to purchase order:", itemsInsertError);
        }
        // Get property name for response
        const propertyName = propertyId === 'general' ? 'General' :
          lowStockItems.find(item => item.property_id === propertyId)?.property_name || 'Unknown';
        createdOrders.push({
          id: order.id,
          propertyName: propertyName,
          itemCount: typedItems.length
        });
      }
      if (createdOrders.length === 0) {
        return {
          success: false,
          message: "Failed to create any purchase orders."
        };
      }
      const orderSummary = createdOrders.map(order =>
        `${order.propertyName} (${order.itemCount} items)`
      ).join(", ");
      return {
        success: true,
        message: `Successfully created ${createdOrders.length} purchase order(s) for low stock items: ${orderSummary}`,
        action: "createPurchaseOrder",
        entityType: "purchase_order",
        entityId: createdOrders[0].id,
        additionalData: {
          orderCount: createdOrders.length,
          orders: createdOrders
        }
      };
    }
    // Handle specific items case
    if (data.items && data.items.length > 0) {
      // Find the specified items in inventory
      const foundItems = [];
      for (const itemName of data.items) {
        const { data: inventoryItems, error: itemError } = await supabase
          .from("inventory_items")
          .select("*")
          .eq("user_id", userId)
          .ilike("name", `%${itemName}%`)
          .limit(5);
        if (itemError) {
          console.error(`Error finding item ${itemName}:`, itemError);
          continue;
        }
        if (inventoryItems && inventoryItems.length > 0) {
          foundItems.push(...inventoryItems);
        }
      }
      if (foundItems.length === 0) {
        return {
          success: false,
          message: `No inventory items found matching: ${data.items.join(", ")}`
        };
      }
      // Group items by property
      const itemsByProperty = {};
      foundItems.forEach(item => {
        const propId = item.property_id || 'general';
        if (!itemsByProperty[propId]) {
          itemsByProperty[propId] = [];
        }
        itemsByProperty[propId].push({
          inventory_item_id: item.id,
          name: item.name,
          quantity: 1, // Default quantity for specific item orders
          price: item.price || 0
        });
      });
      const createdOrders: Array<{ id: any; propertyName: any; itemCount: number }> = [];
      // Create purchase orders for each property
      for (const [propertyId, items] of Object.entries(itemsByProperty)){
        const typedItems = items as any[];
        // Calculate total price
        const totalPrice = typedItems.reduce((sum, item)=>sum + (item.price || 0) * item.quantity, 0);
        // Create the purchase order
        const { data: order, error: orderError } = await supabase.from("purchase_orders").insert({
          user_id: userId,
          property_id: propertyId === 'general' ? null : propertyId,
          status: "pending",
          total_price: totalPrice > 0 ? totalPrice : null,
          notes: data.notes || ""
        }).select().single();
        if (orderError) {
          console.error("Error creating purchase order:", orderError);
          continue;
        }
        // Add items to the purchase order
        const orderItems = typedItems.map(item => ({
          purchase_order_id: order.id,
          inventory_item_id: item.inventory_item_id,
          quantity: item.quantity,
          unit_price: item.price || 0
        }));
        const { error: itemsInsertError } = await supabase.from("purchase_order_items").insert(orderItems);
        if (itemsInsertError) {
          console.error("Error adding items to purchase order:", itemsInsertError);
        }
        // Get property name for response
        const propertyName = propertyId === 'general' ? 'General' :
          foundItems.find(item => item.property_id === propertyId)?.property_name || 'Unknown';
        createdOrders.push({
          id: order.id,
          propertyName: propertyName,
          itemCount: typedItems.length
        });
      }
      if (createdOrders.length === 0) {
        return {
          success: false,
          message: "Failed to create any purchase orders."
        };
      }
      const orderSummary = createdOrders.map(order =>
        `${order.propertyName} (${order.itemCount} items)`
      ).join(", ");
      return {
        success: true,
        message: `Successfully created ${createdOrders.length} purchase order(s) for specified items: ${orderSummary}`,
        action: "createPurchaseOrder",
        entityType: "purchase_order",
        entityId: createdOrders[0].id,
        additionalData: {
          orderCount: createdOrders.length,
          orders: createdOrders
        }
      };
    }
    return {
      success: false,
      message: "Please specify either 'all low stock items' or provide a list of specific items for the purchase order."
    };
  } catch (error) {
    console.error("Error in handleCreatePurchaseOrder:", error);
    return {
      success: false,
      message: `Error creating purchase order: ${error.message}`
    };
  }
}
