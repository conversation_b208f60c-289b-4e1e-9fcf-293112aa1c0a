-- Create RPC function to get user properties by name with partial matching
-- This function is used by the AI command processor to find properties

CREATE OR REPLACE FUNCTION get_user_property_by_name(
  p_user_id UUID,
  p_property_name TEXT
)
RETURNS TABLE (
  id UUID,
  name TEXT,
  address TEXT,
  city TEXT,
  state TEXT,
  zip TEXT,
  bedrooms INTEGER,
  bathrooms INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Return properties that the user has access to (either owns or is a team member)
  -- with partial name matching (case-insensitive)
  RETURN QUERY
  SELECT DISTINCT
    p.id,
    p.name,
    p.address,
    p.city,
    p.state,
    p.zip,
    p.bedrooms,
    p.bathrooms
  FROM properties p
  LEFT JOIN team_properties tp ON p.id = tp.property_id
  LEFT JOIN team_members tm ON tp.team_id = tm.team_id
  WHERE 
    (
      -- User owns the property directly
      p.user_id = p_user_id
      OR
      -- User is an active team member with access to the property
      (tm.user_id = p_user_id AND tm.status = 'active')
    )
    AND
    -- Partial name matching (case-insensitive)
    LOWER(p.name) LIKE LOWER('%' || p_property_name || '%')
  ORDER BY 
    -- Prioritize exact matches, then shorter names (more specific matches first)
    CASE WHEN LOWER(p.name) = LOWER(p_property_name) THEN 1 ELSE 2 END,
    LENGTH(p.name),
    p.name;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_user_property_by_name(UUID, TEXT) TO authenticated;
