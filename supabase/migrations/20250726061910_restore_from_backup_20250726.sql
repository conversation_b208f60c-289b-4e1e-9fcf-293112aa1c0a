-- Database Restore Migration from backup 20250726_041235
-- This migration restores the database from the backup files in db_restore/

-- First, let's create the essential types and enums
DO $$
BEGIN
    -- Create user_role enum if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('property_manager', 'service_provider', 'super_admin');
    END IF;

    -- Create other enums that might be needed
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'maintenance_status') THEN
        CREATE TYPE maintenance_status AS ENUM ('pending', 'in_progress', 'completed', 'cancelled');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'maintenance_severity') THEN
        CREATE TYPE maintenance_severity AS ENUM ('low', 'medium', 'high', 'critical');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'invitation_status') THEN
        CREATE TYPE invitation_status AS ENUM ('pending', 'accepted', 'declined', 'expired');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'team_member_status') THEN
        CREATE TYPE team_member_status AS ENUM ('active', 'inactive', 'pending');
    END IF;
END
$$;

-- Create the profiles table
CREATE TABLE IF NOT EXISTS public.profiles (
    id uuid NOT NULL,
    first_name text,
    last_name text,
    email text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    avatar_url text,
    is_super_admin boolean DEFAULT false NOT NULL,
    role public.user_role DEFAULT 'property_manager'::public.user_role NOT NULL
);

-- Create the teams table
CREATE TABLE IF NOT EXISTS public.teams (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    owner_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Create the team_members table
CREATE TABLE IF NOT EXISTS public.team_members (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    team_id uuid NOT NULL,
    user_id uuid NOT NULL,
    added_by uuid NOT NULL,
    status text DEFAULT 'pending'::text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Create the team_properties table
CREATE TABLE IF NOT EXISTS public.team_properties (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    team_id uuid NOT NULL,
    property_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

-- Create the properties table
CREATE TABLE IF NOT EXISTS public.properties (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    address text NOT NULL,
    city text NOT NULL,
    state text NOT NULL,
    zip text NOT NULL,
    image_url text,
    user_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    bedrooms integer DEFAULT 1,
    bathrooms integer DEFAULT 1,
    budget numeric,
    ical_url text,
    next_booking text,
    collections jsonb,
    last_ical_sync text,
    is_occupied boolean DEFAULT false,
    current_checkout text,
    next_checkin_date text,
    next_checkin_formatted text,
    team_id uuid,
    timezone text DEFAULT 'America/Los_Angeles'::text,
    check_in_time time without time zone DEFAULT '15:00:00'::time without time zone,
    check_out_time time without time zone DEFAULT '11:00:00'::time without time zone
);

-- Add primary key constraints
ALTER TABLE ONLY public.profiles ADD CONSTRAINT profiles_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.teams ADD CONSTRAINT teams_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.team_members ADD CONSTRAINT team_members_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.team_properties ADD CONSTRAINT team_properties_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.properties ADD CONSTRAINT properties_pkey PRIMARY KEY (id);

-- Add foreign key constraints
ALTER TABLE ONLY public.profiles ADD CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;
ALTER TABLE ONLY public.teams ADD CONSTRAINT teams_owner_id_fkey FOREIGN KEY (owner_id) REFERENCES public.profiles(id);
ALTER TABLE ONLY public.team_members ADD CONSTRAINT team_members_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(id);
ALTER TABLE ONLY public.team_members ADD CONSTRAINT team_members_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id);
ALTER TABLE ONLY public.team_properties ADD CONSTRAINT team_properties_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(id);
ALTER TABLE ONLY public.team_properties ADD CONSTRAINT team_properties_property_id_fkey FOREIGN KEY (property_id) REFERENCES public.properties(id);
ALTER TABLE ONLY public.properties ADD CONSTRAINT properties_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id);

-- Insert profiles data (with conflict handling)
INSERT INTO public.profiles (id, first_name, last_name, email, created_at, updated_at, avatar_url, is_super_admin, role) VALUES
('3a1273ce-43cf-422b-91bb-64d587b24702', 'Andrews', 'Arnotts', '<EMAIL>', '2025-07-08 19:49:55.613716+00', '2025-07-08 19:49:55.613716+00', NULL, false, 'service_provider'),
('32356846-1a7c-4367-baa2-d841baed0508', 'Test', 'ServiceProvider', '<EMAIL>', '2025-07-19 08:23:06.651525+00', '2025-07-19 08:23:06.651525+00', NULL, false, 'service_provider'),
('f5e89fc1-d075-4ba4-bdaf-f47498436a03', 'Andrew', 'Arnott', '<EMAIL>', '2025-07-19 08:36:42.153215+00', '2025-07-19 08:36:42.153215+00', NULL, false, 'service_provider'),
('7a4818d9-c2a3-486f-9de2-f6c93c563d27', 'Test', 'User', '<EMAIL>', '2025-07-19 08:45:41.795979+00', '2025-07-19 08:45:41.795979+00', NULL, false, 'service_provider'),
('b1babc3c-f1c3-4506-a844-fe3ddadc2db5', 'Molly', 'Arnott', '<EMAIL>', '2025-07-19 08:50:31.795766+00', '2025-07-19 08:50:56.964058+00', NULL, false, 'service_provider'),
('e4416a70-7490-4c40-a1c4-a5a6aeadf6ea', 'Andrew', 'Arnott', '<EMAIL>', '2025-03-04 06:18:58.425614+00', '2025-03-09 10:12:15.301267+00', NULL, true, 'super_admin'),
('28f9f60c-ac56-4c03-a1d6-567423ad450a', 'Test', 'User', '<EMAIL>', '2025-07-19 08:58:41.7435+00', '2025-07-19 08:58:41.7435+00', NULL, false, 'service_provider'),
('2d915588-3831-44e6-bf07-7a6a0ec0cfce', 'Test', 'User', '<EMAIL>', '2025-07-19 09:08:50.233044+00', '2025-07-19 09:08:50.233044+00', NULL, false, 'service_provider'),
('6799c3f1-9583-4553-92c8-4b95cbdbed16', 'Check', 'User', '<EMAIL>', '2025-07-19 09:09:26.042939+00', '2025-07-19 09:09:26.042939+00', NULL, false, 'service_provider'),
('1c160de0-d510-46dc-845d-265207d368ea', 'Final', 'Test', '<EMAIL>', '2025-07-19 09:11:09.442935+00', '2025-07-19 09:11:09.442935+00', NULL, false, 'service_provider'),
('8dd6072a-61d3-4049-a64c-4683ed0f252e', 'Debug', 'User', '<EMAIL>', '2025-07-19 09:11:51.522573+00', '2025-07-19 09:11:51.522573+00', NULL, false, 'service_provider'),
('d1facda9-7699-40bc-92fe-e998ed8109ae', 'Verify', 'User', '<EMAIL>', '2025-07-19 09:13:14.437711+00', '2025-07-19 09:13:14.437711+00', NULL, false, 'service_provider'),
('9e18266a-1278-4fa4-8c24-121ad1886265', 'Andrews', 'Arnottsd', '<EMAIL>', '2025-07-19 09:16:45.076341+00', '2025-07-19 09:16:45.076341+00', NULL, false, 'service_provider'),
('219a8f90-0127-48f3-aa73-726bef0f22e3', 'Melissa', 'Barr', '<EMAIL>', '2025-07-19 15:27:42.634445+00', '2025-07-19 15:27:42.634445+00', NULL, false, 'service_provider'),
('c749ea63-c8cb-4e8b-b428-9a467755408b', 'Andrew', 'Arnott', '<EMAIL>', '2025-04-17 05:21:18.163259+00', '2025-07-23 18:25:48.767216+00', 'https://pwaeknalhosfwuxkpaet.supabase.co/storage/v1/object/public/avatar-images/c749ea63-c8cb-4e8b-b428-9a467755408b/avatar-1753295148303.jpg', false, 'property_manager'),
('ca7fd3fb-4575-48e5-a6de-f5cdcd156eab', 'eeAndrew', 'Arnottee', '<EMAIL>', '2025-07-08 21:15:54.736248+00', '2025-07-24 17:44:06.120636+00', 'https://pwaeknalhosfwuxkpaet.supabase.co/storage/v1/object/public/avatar-images/avatars/ca7fd3fb-4575-48e5-a6de-f5cdcd156eab/lion-9699734_1753379045320_679xudr1.webp', false, 'service_provider'),
('4a72122e-c35f-4a51-ae6f-3f085248fc39', 'Ashlee', 'Glass', '<EMAIL>', '2025-04-30 04:36:25.760417+00', '2025-04-30 04:36:25.760417+00', NULL, false, 'service_provider'),
('1d842b24-1e25-4bf9-aa65-6f59c0f831fb', 'Molly', 'Arnott', '<EMAIL>', '2025-04-07 20:51:33.698588+00', '2025-04-20 04:03:02.769855+00', NULL, false, 'property_manager')
ON CONFLICT (id) DO UPDATE SET
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    email = EXCLUDED.email,
    avatar_url = EXCLUDED.avatar_url,
    is_super_admin = EXCLUDED.is_super_admin,
    role = EXCLUDED.role,
    updated_at = EXCLUDED.updated_at;

-- Insert teams data (with conflict handling)
INSERT INTO public.teams (id, name, owner_id, created_at, updated_at) VALUES
('af45b991-3a25-4bc7-afec-95a2db9823e3', 'Andrew''s Team', 'c749ea63-c8cb-4e8b-b428-9a467755408b', '2025-04-17 05:27:38.773778+00', '2025-04-17 05:27:38.773778+00'),
('814cd478-0a1a-4070-ad21-99d7326db7cd', 'Molly', 'e4416a70-7490-4c40-a1c4-a5a6aeadf6ea', '2025-03-09 07:55:30.163713+00', '2025-04-05 22:17:47.941308+00'),
('465e5a17-400b-4625-8642-f2fb74d2a57b', 'Andy', 'e4416a70-7490-4c40-a1c4-a5a6aeadf6ea', '2025-04-11 03:31:17.154887+00', '2025-04-11 03:31:17.154887+00'),
('2fbcb5cb-e9b3-4789-b6a0-515309327a1b', 'Molly''s Team', '1d842b24-1e25-4bf9-aa65-6f59c0f831fb', '2025-04-17 05:35:52.009162+00', '2025-04-17 05:35:52.009162+00'),
('b0c6809c-ff15-41f9-a881-bbe9bf23f28a', 'Nebraska Properties', 'c749ea63-c8cb-4e8b-b428-9a467755408b', '2025-04-17 16:18:00.362217+00', '2025-04-17 16:18:00.362217+00'),
('3b9e7651-68c3-432f-9a28-7440139250f3', 'All Properties', 'c749ea63-c8cb-4e8b-b428-9a467755408b', '2025-04-17 16:49:27.096546+00', '2025-04-17 16:49:27.096546+00'),
('80145fbc-b0b5-421f-acff-5c20a1e573e8', 'Idaho Properties', 'c749ea63-c8cb-4e8b-b428-9a467755408b', '2025-04-17 16:21:38.717265+00', '2025-07-23 18:45:04.692253+00')
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    owner_id = EXCLUDED.owner_id,
    updated_at = EXCLUDED.updated_at;