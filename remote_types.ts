export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      automation_queue: {
        Row: {
          booking_id: string
          created_at: string | null
          id: string
          processed: boolean | null
          processed_at: string | null
        }
        Insert: {
          booking_id: string
          created_at?: string | null
          id?: string
          processed?: boolean | null
          processed_at?: string | null
        }
        Update: {
          booking_id?: string
          created_at?: string | null
          id?: string
          processed?: boolean | null
          processed_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "automation_queue_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "bookings"
            referencedColumns: ["id"]
          },
        ]
      }
      automation_rules: {
        Row: {
          assigned_to: string | null
          created_at: string | null
          description: string | null
          id: string
          name: string
          property_ids: Json | null
          severity: string
          task_type: string
          time_offset: number
          title: string
          trigger_type: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          assigned_to?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          property_ids?: Json | null
          severity: string
          task_type: string
          time_offset: number
          title: string
          trigger_type: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          assigned_to?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          property_ids?: Json | null
          severity?: string
          task_type?: string
          time_offset?: number
          title?: string
          trigger_type?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      backups: {
        Row: {
          backup_data: Json | null
          created_at: string | null
          id: number
          includes_auth: boolean
          includes_database: boolean
          includes_edge_functions: boolean
          includes_storage: boolean
          schema: Json | null
          status: string
          updated_at: string | null
        }
        Insert: {
          backup_data?: Json | null
          created_at?: string | null
          id?: never
          includes_auth: boolean
          includes_database: boolean
          includes_edge_functions: boolean
          includes_storage: boolean
          schema?: Json | null
          status: string
          updated_at?: string | null
        }
        Update: {
          backup_data?: Json | null
          created_at?: string | null
          id?: never
          includes_auth?: boolean
          includes_database?: boolean
          includes_edge_functions?: boolean
          includes_storage?: boolean
          schema?: Json | null
          status?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      bookings: {
        Row: {
          check_in_date: string
          check_out_date: string
          created_at: string | null
          id: string
          property_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          check_in_date: string
          check_out_date: string
          created_at?: string | null
          id?: string
          property_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          check_in_date?: string
          check_out_date?: string
          created_at?: string | null
          id?: string
          property_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "bookings_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      collections: {
        Row: {
          created_at: string
          id: string
          name: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "collections_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      damage_invoices: {
        Row: {
          created_at: string
          damage_report_id: string
          due_date: string | null
          file_name: string | null
          file_path: string | null
          file_url: string | null
          id: string
          invoice_number: string | null
          issue_date: string | null
          notes: string | null
          provider_id: string | null
          status: string
          total_amount: number | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          damage_report_id: string
          due_date?: string | null
          file_name?: string | null
          file_path?: string | null
          file_url?: string | null
          id?: string
          invoice_number?: string | null
          issue_date?: string | null
          notes?: string | null
          provider_id?: string | null
          status?: string
          total_amount?: number | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          damage_report_id?: string
          due_date?: string | null
          file_name?: string | null
          file_path?: string | null
          file_url?: string | null
          id?: string
          invoice_number?: string | null
          issue_date?: string | null
          notes?: string | null
          provider_id?: string | null
          status?: string
          total_amount?: number | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "damage_invoices_damage_report_id_fkey"
            columns: ["damage_report_id"]
            isOneToOne: false
            referencedRelation: "damage_reports"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "damage_invoices_damage_report_id_fkey"
            columns: ["damage_report_id"]
            isOneToOne: false
            referencedRelation: "damage_reports_with_property"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "damage_invoices_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "maintenance_providers"
            referencedColumns: ["id"]
          },
        ]
      }
      damage_notes: {
        Row: {
          content: string
          created_at: string
          created_by: string | null
          damage_report_id: string
          id: string
          private: boolean
          updated_at: string
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string
          created_by?: string | null
          damage_report_id: string
          id?: string
          private?: boolean
          updated_at?: string
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string
          created_by?: string | null
          damage_report_id?: string
          id?: string
          private?: boolean
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "damage_notes_damage_report_id_fkey"
            columns: ["damage_report_id"]
            isOneToOne: false
            referencedRelation: "damage_reports"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "damage_notes_damage_report_id_fkey"
            columns: ["damage_report_id"]
            isOneToOne: false
            referencedRelation: "damage_reports_with_property"
            referencedColumns: ["id"]
          },
        ]
      }
      damage_photos: {
        Row: {
          caption: string | null
          created_at: string
          damage_report_id: string
          file_name: string
          file_path: string
          id: string
          media_type: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          caption?: string | null
          created_at?: string
          damage_report_id: string
          file_name: string
          file_path: string
          id?: string
          media_type?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          caption?: string | null
          created_at?: string
          damage_report_id?: string
          file_name?: string
          file_path?: string
          id?: string
          media_type?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "damage_photos_damage_report_id_fkey"
            columns: ["damage_report_id"]
            isOneToOne: false
            referencedRelation: "damage_reports"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "damage_photos_damage_report_id_fkey"
            columns: ["damage_report_id"]
            isOneToOne: false
            referencedRelation: "damage_reports_with_property"
            referencedColumns: ["id"]
          },
        ]
      }
      damage_reports: {
        Row: {
          created_at: string
          description: string
          id: string
          platform: string | null
          property_id: string
          provider_id: string | null
          status: string
          team_id: string | null
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          description: string
          id?: string
          platform?: string | null
          property_id: string
          provider_id?: string | null
          status?: string
          team_id?: string | null
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          description?: string
          id?: string
          platform?: string | null
          property_id?: string
          provider_id?: string | null
          status?: string
          team_id?: string | null
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "damage_reports_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "damage_reports_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "maintenance_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "damage_reports_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "damage_reports_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      extension_api_tokens: {
        Row: {
          created_at: string | null
          id: string
          last_used: string | null
          metadata: Json | null
          revoked_at: string | null
          token_hash: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          last_used?: string | null
          metadata?: Json | null
          revoked_at?: string | null
          token_hash: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          last_used?: string | null
          metadata?: Json | null
          revoked_at?: string | null
          token_hash?: string
          user_id?: string | null
        }
        Relationships: []
      }
      inventory_items: {
        Row: {
          amazon_url: string | null
          asin: string | null
          collection: string
          collection_id: string | null
          created_at: string
          id: string
          image_url: string | null
          min_quantity: number
          name: string
          price: number | null
          property_id: string
          quantity: number
          team_id: string | null
          updated_at: string
          user_id: string
          walmart_item_id: string | null
          walmart_url: string | null
        }
        Insert: {
          amazon_url?: string | null
          asin?: string | null
          collection: string
          collection_id?: string | null
          created_at?: string
          id?: string
          image_url?: string | null
          min_quantity?: number
          name: string
          price?: number | null
          property_id: string
          quantity?: number
          team_id?: string | null
          updated_at?: string
          user_id: string
          walmart_item_id?: string | null
          walmart_url?: string | null
        }
        Update: {
          amazon_url?: string | null
          asin?: string | null
          collection?: string
          collection_id?: string | null
          created_at?: string
          id?: string
          image_url?: string | null
          min_quantity?: number
          name?: string
          price?: number | null
          property_id?: string
          quantity?: number
          team_id?: string | null
          updated_at?: string
          user_id?: string
          walmart_item_id?: string | null
          walmart_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_items_collection_id_fkey"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "collections"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_items_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_items_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_items_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      invitations: {
        Row: {
          created_at: string
          email: string
          expires_at: string
          id: string
          invited_by: string
          role: Database["public"]["Enums"]["user_role"]
          status: string
          team_id: string
          token: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          email: string
          expires_at: string
          id?: string
          invited_by: string
          role: Database["public"]["Enums"]["user_role"]
          status?: string
          team_id: string
          token: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          email?: string
          expires_at?: string
          id?: string
          invited_by?: string
          role?: Database["public"]["Enums"]["user_role"]
          status?: string
          team_id?: string
          token?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "invitations_invited_by_fkey"
            columns: ["invited_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      invoice_items: {
        Row: {
          amount: number
          created_at: string
          description: string
          id: string
          invoice_id: string
          quantity: number
          unit_price: number
          updated_at: string
        }
        Insert: {
          amount: number
          created_at?: string
          description: string
          id?: string
          invoice_id: string
          quantity: number
          unit_price: number
          updated_at?: string
        }
        Update: {
          amount?: number
          created_at?: string
          description?: string
          id?: string
          invoice_id?: string
          quantity?: number
          unit_price?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "invoice_items_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "damage_invoices"
            referencedColumns: ["id"]
          },
        ]
      }
      maintenance_providers: {
        Row: {
          created_at: string
          email: string | null
          id: string
          name: string
          notes: string | null
          phone: string | null
          specialty: string | null
          team_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          email?: string | null
          id?: string
          name: string
          notes?: string | null
          phone?: string | null
          specialty?: string | null
          team_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          email?: string | null
          id?: string
          name?: string
          notes?: string | null
          phone?: string | null
          specialty?: string | null
          team_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "maintenance_providers_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "maintenance_providers_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      maintenance_requests: {
        Row: {
          created_at: string
          description: string
          id: string
          priority: string
          property_id: string
          provider_id: string | null
          status: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          description: string
          id?: string
          priority?: string
          property_id: string
          provider_id?: string | null
          status?: string
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          description?: string
          id?: string
          priority?: string
          property_id?: string
          provider_id?: string | null
          status?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "maintenance_requests_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "maintenance_requests_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      maintenance_tasks: {
        Row: {
          assigned_to: string | null
          completed_at: string | null
          created_at: string
          description: string | null
          due_date: string | null
          email_notification_sent: boolean | null
          email_notification_sent_at: string | null
          id: string
          is_recurring: boolean | null
          max_recurrences: number | null
          next_due_date: string | null
          parent_task_id: string | null
          property_id: string | null
          property_name: string
          provider_email: string | null
          provider_id: string | null
          recurrence_count: number | null
          recurrence_interval_days: number | null
          severity: string
          status: string
          team_id: string | null
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          assigned_to?: string | null
          completed_at?: string | null
          created_at?: string
          description?: string | null
          due_date?: string | null
          email_notification_sent?: boolean | null
          email_notification_sent_at?: string | null
          id?: string
          is_recurring?: boolean | null
          max_recurrences?: number | null
          next_due_date?: string | null
          parent_task_id?: string | null
          property_id?: string | null
          property_name: string
          provider_email?: string | null
          provider_id?: string | null
          recurrence_count?: number | null
          recurrence_interval_days?: number | null
          severity?: string
          status?: string
          team_id?: string | null
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          assigned_to?: string | null
          completed_at?: string | null
          created_at?: string
          description?: string | null
          due_date?: string | null
          email_notification_sent?: boolean | null
          email_notification_sent_at?: string | null
          id?: string
          is_recurring?: boolean | null
          max_recurrences?: number | null
          next_due_date?: string | null
          parent_task_id?: string | null
          property_id?: string | null
          property_name?: string
          provider_email?: string | null
          provider_id?: string | null
          recurrence_count?: number | null
          recurrence_interval_days?: number | null
          severity?: string
          status?: string
          team_id?: string | null
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "maintenance_tasks_parent_task_id_fkey"
            columns: ["parent_task_id"]
            isOneToOne: false
            referencedRelation: "maintenance_tasks"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "maintenance_tasks_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "maintenance_tasks_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          email: string
          first_name: string | null
          id: string
          is_super_admin: boolean
          last_name: string | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          email: string
          first_name?: string | null
          id: string
          is_super_admin?: boolean
          last_name?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          email?: string
          first_name?: string | null
          id?: string
          is_super_admin?: boolean
          last_name?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string
        }
        Relationships: []
      }
      properties: {
        Row: {
          address: string
          bathrooms: number | null
          bedrooms: number | null
          budget: number | null
          check_in_time: string | null
          check_out_time: string | null
          city: string
          collections: Json | null
          created_at: string
          current_checkout: string | null
          ical_url: string | null
          id: string
          image_url: string | null
          is_occupied: boolean | null
          last_ical_sync: string | null
          name: string
          next_booking: string | null
          next_checkin_date: string | null
          next_checkin_formatted: string | null
          state: string
          team_id: string | null
          timezone: string | null
          updated_at: string
          user_id: string
          zip: string
        }
        Insert: {
          address: string
          bathrooms?: number | null
          bedrooms?: number | null
          budget?: number | null
          check_in_time?: string | null
          check_out_time?: string | null
          city: string
          collections?: Json | null
          created_at?: string
          current_checkout?: string | null
          ical_url?: string | null
          id?: string
          image_url?: string | null
          is_occupied?: boolean | null
          last_ical_sync?: string | null
          name: string
          next_booking?: string | null
          next_checkin_date?: string | null
          next_checkin_formatted?: string | null
          state: string
          team_id?: string | null
          timezone?: string | null
          updated_at?: string
          user_id: string
          zip: string
        }
        Update: {
          address?: string
          bathrooms?: number | null
          bedrooms?: number | null
          budget?: number | null
          check_in_time?: string | null
          check_out_time?: string | null
          city?: string
          collections?: Json | null
          created_at?: string
          current_checkout?: string | null
          ical_url?: string | null
          id?: string
          image_url?: string | null
          is_occupied?: boolean | null
          last_ical_sync?: string | null
          name?: string
          next_booking?: string | null
          next_checkin_date?: string | null
          next_checkin_formatted?: string | null
          state?: string
          team_id?: string | null
          timezone?: string | null
          updated_at?: string
          user_id?: string
          zip?: string
        }
        Relationships: [
          {
            foreignKeyName: "properties_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "properties_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      property_documents: {
        Row: {
          content: string
          created_at: string | null
          id: string
          is_private: boolean
          property_id: string
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          is_private?: boolean
          property_id: string
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          is_private?: boolean
          property_id?: string
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "property_documents_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      property_files: {
        Row: {
          caption: string | null
          created_at: string | null
          display_name: string | null
          file_path: string
          file_size: number
          file_type: string
          filename: string
          id: string
          is_private: boolean
          property_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          caption?: string | null
          created_at?: string | null
          display_name?: string | null
          file_path: string
          file_size: number
          file_type: string
          filename: string
          id?: string
          is_private?: boolean
          property_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          caption?: string | null
          created_at?: string | null
          display_name?: string | null
          file_path?: string
          file_size?: number
          file_type?: string
          filename?: string
          id?: string
          is_private?: boolean
          property_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "property_files_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      provider_teams: {
        Row: {
          created_at: string
          id: string
          provider_id: string
          team_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          provider_id: string
          team_id: string
        }
        Update: {
          created_at?: string
          id?: string
          provider_id?: string
          team_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "provider_teams_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "maintenance_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "provider_teams_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      purchase_order_items: {
        Row: {
          amazon_url: string | null
          created_at: string
          id: string
          inventory_item_id: string | null
          item_name: string
          price: number | null
          purchase_order_id: string
          quantity: number
          walmart_url: string | null
        }
        Insert: {
          amazon_url?: string | null
          created_at?: string
          id?: string
          inventory_item_id?: string | null
          item_name: string
          price?: number | null
          purchase_order_id: string
          quantity: number
          walmart_url?: string | null
        }
        Update: {
          amazon_url?: string | null
          created_at?: string
          id?: string
          inventory_item_id?: string | null
          item_name?: string
          price?: number | null
          purchase_order_id?: string
          quantity?: number
          walmart_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "purchase_order_items_inventory_item_id_fkey"
            columns: ["inventory_item_id"]
            isOneToOne: false
            referencedRelation: "inventory_items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_order_items_purchase_order_id_fkey"
            columns: ["purchase_order_id"]
            isOneToOne: false
            referencedRelation: "purchase_orders"
            referencedColumns: ["id"]
          },
        ]
      }
      purchase_orders: {
        Row: {
          created_at: string
          id: string
          is_archived: boolean
          notes: string | null
          property_id: string
          status: Database["public"]["Enums"]["po_status"]
          team_id: string | null
          total_price: number | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          is_archived?: boolean
          notes?: string | null
          property_id: string
          status?: Database["public"]["Enums"]["po_status"]
          team_id?: string | null
          total_price?: number | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          is_archived?: boolean
          notes?: string | null
          property_id?: string
          status?: Database["public"]["Enums"]["po_status"]
          team_id?: string | null
          total_price?: number | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "purchase_orders_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_orders_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_orders_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      service_providers: {
        Row: {
          created_at: string | null
          email: string
          first_name: string | null
          id: string
          last_name: string | null
          status: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          first_name?: string | null
          id: string
          last_name?: string | null
          status?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          first_name?: string | null
          id?: string
          last_name?: string | null
          status?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      team_invitations: {
        Row: {
          accepted_at: string | null
          created_at: string
          email: string
          expires_at: string
          id: string
          invited_by: string
          role: Database["public"]["Enums"]["user_role"]
          status: string
          team_id: string
          team_name: string | null
          token: string
          updated_at: string
        }
        Insert: {
          accepted_at?: string | null
          created_at?: string
          email: string
          expires_at: string
          id?: string
          invited_by: string
          role: Database["public"]["Enums"]["user_role"]
          status?: string
          team_id: string
          team_name?: string | null
          token: string
          updated_at?: string
        }
        Update: {
          accepted_at?: string | null
          created_at?: string
          email?: string
          expires_at?: string
          id?: string
          invited_by?: string
          role?: Database["public"]["Enums"]["user_role"]
          status?: string
          team_id?: string
          team_name?: string | null
          token?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_invitations_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      team_members: {
        Row: {
          added_by: string
          created_at: string
          id: string
          status: string
          team_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          added_by: string
          created_at?: string
          id?: string
          status?: string
          team_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          added_by?: string
          created_at?: string
          id?: string
          status?: string
          team_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_members_added_by_fkey"
            columns: ["added_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_members_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      team_properties: {
        Row: {
          created_at: string
          id: string
          property_id: string
          team_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          property_id: string
          team_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          property_id?: string
          team_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_properties_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_properties_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      teams: {
        Row: {
          created_at: string
          id: string
          name: string
          owner_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          owner_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          owner_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "teams_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_permissions: {
        Row: {
          created_at: string
          enabled: boolean
          id: string
          permission: Database["public"]["Enums"]["permission_type"]
          team_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          enabled?: boolean
          id?: string
          permission: Database["public"]["Enums"]["permission_type"]
          team_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          enabled?: boolean
          id?: string
          permission?: Database["public"]["Enums"]["permission_type"]
          team_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_permissions_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_permissions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_preferences: {
        Row: {
          created_at: string
          id: string
          onboarding_state: Json
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          onboarding_state?: Json
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          onboarding_state?: Json
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      user_settings: {
        Row: {
          animations: boolean
          compact_mode: boolean
          created_at: string
          dark_mode: boolean
          email_notifications: boolean
          id: string
          inventory_alerts: boolean
          push_notifications: boolean
          updated_at: string
          user_id: string
          weekly_summary: boolean
        }
        Insert: {
          animations?: boolean
          compact_mode?: boolean
          created_at?: string
          dark_mode?: boolean
          email_notifications?: boolean
          id?: string
          inventory_alerts?: boolean
          push_notifications?: boolean
          updated_at?: string
          user_id: string
          weekly_summary?: boolean
        }
        Update: {
          animations?: boolean
          compact_mode?: boolean
          created_at?: string
          dark_mode?: boolean
          email_notifications?: boolean
          id?: string
          inventory_alerts?: boolean
          push_notifications?: boolean
          updated_at?: string
          user_id?: string
          weekly_summary?: boolean
        }
        Relationships: []
      }
    }
    Views: {
      damage_reports_with_property: {
        Row: {
          created_at: string | null
          description: string | null
          id: string | null
          platform: string | null
          property_id: string | null
          property_name: string | null
          property_owner_id: string | null
          provider_id: string | null
          provider_name: string | null
          status: string | null
          team_id: string | null
          title: string | null
          updated_at: string | null
          user_email: string | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "damage_reports_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "damage_reports_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "maintenance_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "damage_reports_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "damage_reports_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "properties_user_id_fkey"
            columns: ["property_owner_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      team_members_with_profiles: {
        Row: {
          added_by: string | null
          avatar_url: string | null
          created_at: string | null
          email: string | null
          first_name: string | null
          id: string | null
          is_super_admin: boolean | null
          last_name: string | null
          profile_role: Database["public"]["Enums"]["user_role"] | null
          status: string | null
          team_id: string | null
          updated_at: string | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "team_members_added_by_fkey"
            columns: ["added_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_members_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      accept_invitation_and_add_member: {
        Args: { p_token: string; p_user_id: string }
        Returns: Json
      }
      accept_invitation_direct: {
        Args: {
          p_token: string
          p_email: string
          p_first_name: string
          p_last_name: string
          p_password: string
          p_role?: string
        }
        Returns: Json
      }
      accept_team_invitation: {
        Args: { invitation_token: string; accepting_user_id: string }
        Returns: boolean
      }
      accept_team_invitation_safe: {
        Args: { invitation_token: string; accepting_user_id: string }
        Returns: boolean
      }
      add_property_to_team_direct: {
        Args: { p_team_id: string; p_property_id: string; p_user_id: string }
        Returns: boolean
      }
      add_property_to_team_safe: {
        Args: { p_team_id: string; p_property_id: string; p_user_id: string }
        Returns: boolean
      }
      add_service_provider_default_permissions: {
        Args: { p_user_id: string; p_team_id: string }
        Returns: boolean
      }
      add_user_to_team: {
        Args: { p_user_id: string; p_team_id: string; p_added_by?: string }
        Returns: Json
      }
      can_access_damage_report: {
        Args: { report_id: string }
        Returns: boolean
      }
      can_create_maintenance_task: {
        Args: { p_property_id: string }
        Returns: boolean
      }
      can_manage_invitations: {
        Args: { target_team_id: string }
        Returns: boolean
      }
      can_manage_invitations_safe: {
        Args: { target_team_id: string }
        Returns: boolean
      }
      can_manage_permissions_safe: {
        Args: { target_team_id: string }
        Returns: boolean
      }
      can_manage_service_providers: {
        Args: { user_id: string }
        Returns: boolean
      }
      can_manage_staff: {
        Args: { user_id: string }
        Returns: boolean
      }
      can_manage_team_members: {
        Args: { target_team_id: string }
        Returns: boolean
      }
      can_manage_team_permissions: {
        Args: { target_team_id: string }
        Returns: boolean
      }
      check_property_in_team: {
        Args: { p_property_id: string; p_team_id: string }
        Returns: boolean
      }
      create_next_recurring_task: {
        Args: { completed_task_id: string }
        Returns: string
      }
      create_profile: {
        Args: {
          p_id: string
          p_email: string
          p_first_name?: string
          p_last_name?: string
          p_role?: string
          p_is_super_admin?: boolean
        }
        Returns: {
          avatar_url: string | null
          created_at: string
          email: string
          first_name: string | null
          id: string
          is_super_admin: boolean
          last_name: string | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string
        }[]
      }
      create_profile_safely: {
        Args: {
          p_user_id: string
          p_first_name: string
          p_last_name: string
          p_role: string
          p_email: string
        }
        Returns: Json
      }
      create_service_provider_profile: {
        Args: {
          p_id: string
          p_email: string
          p_first_name: string
          p_last_name: string
          p_status: string
        }
        Returns: boolean
      }
      create_team_invitation: {
        Args:
          | {
              p_team_id: string
              p_email: string
              p_role: Database["public"]["Enums"]["user_role"]
              p_invited_by: string
              p_token?: string
              p_expires_in?: unknown
            }
          | {
              p_team_id: string
              p_email: string
              p_role: string
              p_invited_by: string
              p_token?: string
              p_expires_in?: unknown
            }
          | {
              team_id: string
              email: string
              role: Database["public"]["Enums"]["user_role"]
              expires_in?: unknown
            }
          | {
              team_id: string
              email: string
              role: Database["public"]["Enums"]["user_role"]
              invitation_token: string
              expires_in?: unknown
            }
        Returns: string
      }
      create_user_preferences: {
        Args: { p_user_id: string; p_onboarding_state: Json }
        Returns: boolean
      }
      debug_team_access: {
        Args: { p_team_id: string; p_user_id: string }
        Returns: Json
      }
      delete_property_cascade: {
        Args: { property_id_param: string }
        Returns: undefined
      }
      delete_team_cascade: {
        Args: { team_id_param: string }
        Returns: undefined
      }
      ensure_all_users_have_profiles: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      ensure_profile_exists: {
        Args: {
          p_id: string
          p_email: string
          p_first_name?: string
          p_last_name?: string
          p_role?: string
        }
        Returns: {
          id: string
          email: string
          first_name: string
          last_name: string
          avatar_url: string
          is_super_admin: boolean
          role: Database["public"]["Enums"]["user_role"]
          created_at: string
          updated_at: string
        }[]
      }
      execute_sql: {
        Args: { query: string }
        Returns: Json
      }
      execute_sql_query: {
        Args: { sql_query: string }
        Returns: undefined
      }
      fix_team_invitation: {
        Args: { p_email: string; p_team_id: string }
        Returns: Json
      }
      get_all_damage_reports: {
        Args: { p_user_id: string }
        Returns: {
          id: string
          title: string
          description: string
          status: string
          property_id: string
          property_name: string
          user_id: string
          created_at: string
          updated_at: string
          provider_id: string
          provider_name: string
          platform: string
        }[]
      }
      get_all_tables: {
        Args: Record<PropertyKey, never>
        Returns: {
          tablename: string
        }[]
      }
      get_damage_notes: {
        Args: { p_damage_report_id: string; p_include_private?: boolean }
        Returns: {
          id: string
          damage_report_id: string
          content: string
          user_id: string
          created_at: string
          updated_at: string
          private: boolean
          created_by: string
        }[]
      }
      get_damage_photo_url: {
        Args: { p_photo_path: string }
        Returns: string
      }
      get_damage_photos: {
        Args: { p_damage_report_id: string }
        Returns: {
          id: string
          damage_report_id: string
          file_name: string
          file_path: string
          caption: string
          user_id: string
          created_at: string
          updated_at: string
          media_type: string
          public_url: string
        }[]
      }
      get_database_size: {
        Args: Record<PropertyKey, never>
        Returns: {
          size_bytes: string
          size_pretty: string
        }[]
      }
      get_functions: {
        Args: Record<PropertyKey, never>
        Returns: {
          function_name: string
        }[]
      }
      get_low_stock_items: {
        Args: { user_id_param: string; property_id_param?: string }
        Returns: {
          id: string
          name: string
          quantity: number
          min_quantity: number
          property_id: string
          collection: string
          price: number
          amazon_url: string
          walmart_url: string
          property_name: string
        }[]
      }
      get_maintenance_tasks_for_team_member: {
        Args: Record<PropertyKey, never>
        Returns: {
          assigned_to: string | null
          completed_at: string | null
          created_at: string
          description: string | null
          due_date: string | null
          email_notification_sent: boolean | null
          email_notification_sent_at: string | null
          id: string
          is_recurring: boolean | null
          max_recurrences: number | null
          next_due_date: string | null
          parent_task_id: string | null
          property_id: string | null
          property_name: string
          provider_email: string | null
          provider_id: string | null
          recurrence_count: number | null
          recurrence_interval_days: number | null
          severity: string
          status: string
          team_id: string | null
          title: string
          updated_at: string
          user_id: string
        }[]
      }
      get_maintenance_tasks_for_user: {
        Args: { p_user_id: string }
        Returns: {
          assigned_to: string | null
          completed_at: string | null
          created_at: string
          description: string | null
          due_date: string | null
          email_notification_sent: boolean | null
          email_notification_sent_at: string | null
          id: string
          is_recurring: boolean | null
          max_recurrences: number | null
          next_due_date: string | null
          parent_task_id: string | null
          property_id: string | null
          property_name: string
          provider_email: string | null
          provider_id: string | null
          recurrence_count: number | null
          recurrence_interval_days: number | null
          severity: string
          status: string
          team_id: string | null
          title: string
          updated_at: string
          user_id: string
        }[]
      }
      get_profile: {
        Args: { p_user_id: string }
        Returns: {
          avatar_url: string | null
          created_at: string
          email: string
          first_name: string | null
          id: string
          is_super_admin: boolean
          last_name: string | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string
        }[]
      }
      get_profile_by_id: {
        Args: { user_id: string }
        Returns: {
          avatar_url: string | null
          created_at: string
          email: string
          first_name: string | null
          id: string
          is_super_admin: boolean
          last_name: string | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string
        }[]
      }
      get_profile_by_id_or_email: {
        Args: { p_id?: string; p_email?: string }
        Returns: {
          avatar_url: string | null
          created_at: string
          email: string
          first_name: string | null
          id: string
          is_super_admin: boolean
          last_name: string | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string
        }[]
      }
      get_properties_for_team_member: {
        Args: { p_user_id: string; p_team_id: string }
        Returns: {
          address: string
          bathrooms: number | null
          bedrooms: number | null
          budget: number | null
          check_in_time: string | null
          check_out_time: string | null
          city: string
          collections: Json | null
          created_at: string
          current_checkout: string | null
          ical_url: string | null
          id: string
          image_url: string | null
          is_occupied: boolean | null
          last_ical_sync: string | null
          name: string
          next_booking: string | null
          next_checkin_date: string | null
          next_checkin_formatted: string | null
          state: string
          team_id: string | null
          timezone: string | null
          updated_at: string
          user_id: string
          zip: string
        }[]
      }
      get_property_damage_reports: {
        Args: { p_property_id: string }
        Returns: {
          id: string
          title: string
          description: string
          status: string
          property_id: string
          property_name: string
          user_id: string
          user_email: string
          provider_id: string
          provider_name: string
          created_at: string
          updated_at: string
          platform: string
        }[]
      }
      get_property_details: {
        Args: { p_property_id: string }
        Returns: {
          address: string
          bathrooms: number | null
          bedrooms: number | null
          budget: number | null
          check_in_time: string | null
          check_out_time: string | null
          city: string
          collections: Json | null
          created_at: string
          current_checkout: string | null
          ical_url: string | null
          id: string
          image_url: string | null
          is_occupied: boolean | null
          last_ical_sync: string | null
          name: string
          next_booking: string | null
          next_checkin_date: string | null
          next_checkin_formatted: string | null
          state: string
          team_id: string | null
          timezone: string | null
          updated_at: string
          user_id: string
          zip: string
        }[]
      }
      get_property_details_simple: {
        Args: { p_property_id: string }
        Returns: {
          address: string
          bathrooms: number | null
          bedrooms: number | null
          budget: number | null
          check_in_time: string | null
          check_out_time: string | null
          city: string
          collections: Json | null
          created_at: string
          current_checkout: string | null
          ical_url: string | null
          id: string
          image_url: string | null
          is_occupied: boolean | null
          last_ical_sync: string | null
          name: string
          next_booking: string | null
          next_checkin_date: string | null
          next_checkin_formatted: string | null
          state: string
          team_id: string | null
          timezone: string | null
          updated_at: string
          user_id: string
          zip: string
        }[]
      }
      get_property_if_team_member: {
        Args: { p_property_id: string; p_user_id: string }
        Returns: {
          address: string
          bathrooms: number | null
          bedrooms: number | null
          budget: number | null
          check_in_time: string | null
          check_out_time: string | null
          city: string
          collections: Json | null
          created_at: string
          current_checkout: string | null
          ical_url: string | null
          id: string
          image_url: string | null
          is_occupied: boolean | null
          last_ical_sync: string | null
          name: string
          next_booking: string | null
          next_checkin_date: string | null
          next_checkin_formatted: string | null
          state: string
          team_id: string | null
          timezone: string | null
          updated_at: string
          user_id: string
          zip: string
        }[]
      }
      get_property_with_team_access: {
        Args: { p_property_id: string; p_user_id: string }
        Returns: {
          address: string
          bathrooms: number | null
          bedrooms: number | null
          budget: number | null
          check_in_time: string | null
          check_out_time: string | null
          city: string
          collections: Json | null
          created_at: string
          current_checkout: string | null
          ical_url: string | null
          id: string
          image_url: string | null
          is_occupied: boolean | null
          last_ical_sync: string | null
          name: string
          next_booking: string | null
          next_checkin_date: string | null
          next_checkin_formatted: string | null
          state: string
          team_id: string | null
          timezone: string | null
          updated_at: string
          user_id: string
          zip: string
        }[]
      }
      get_providers: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          email: string
          phone: string
          specialty: string
          notes: string
          user_id: string
          created_at: string
          updated_at: string
          provider_type: string
        }[]
      }
      get_recurring_task_series: {
        Args: { p_task_id: string }
        Returns: {
          task_id: string
          task_title: string
          task_status: string
          task_due_date: string
          task_completed_at: string
          task_recurrence_count: number
          task_is_original: boolean
        }[]
      }
      get_service_provider_properties: {
        Args: { p_user_id: string }
        Returns: {
          address: string
          bathrooms: number | null
          bedrooms: number | null
          budget: number | null
          check_in_time: string | null
          check_out_time: string | null
          city: string
          collections: Json | null
          created_at: string
          current_checkout: string | null
          ical_url: string | null
          id: string
          image_url: string | null
          is_occupied: boolean | null
          last_ical_sync: string | null
          name: string
          next_booking: string | null
          next_checkin_date: string | null
          next_checkin_formatted: string | null
          state: string
          team_id: string | null
          timezone: string | null
          updated_at: string
          user_id: string
          zip: string
        }[]
      }
      get_table_columns: {
        Args: { table_name: string }
        Returns: {
          column_name: string
          data_type: string
          is_nullable: boolean
          column_default: string
        }[]
      }
      get_table_size: {
        Args: { table_name: string }
        Returns: {
          size_bytes: string
          size_pretty: string
        }[]
      }
      get_table_structure: {
        Args: { table_name: string }
        Returns: {
          column_name: string
          data_type: string
          is_nullable: boolean
          column_default: string
        }[]
      }
      get_tables: {
        Args: Record<PropertyKey, never>
        Returns: {
          table_name: string
        }[]
      }
      get_tasks_for_properties_or_user: {
        Args: { property_ids: string[]; input_user_id: string }
        Returns: {
          assigned_to: string | null
          completed_at: string | null
          created_at: string
          description: string | null
          due_date: string | null
          email_notification_sent: boolean | null
          email_notification_sent_at: string | null
          id: string
          is_recurring: boolean | null
          max_recurrences: number | null
          next_due_date: string | null
          parent_task_id: string | null
          property_id: string | null
          property_name: string
          provider_email: string | null
          provider_id: string | null
          recurrence_count: number | null
          recurrence_interval_days: number | null
          severity: string
          status: string
          team_id: string | null
          title: string
          updated_at: string
          user_id: string
        }[]
      }
      get_team_damage_reports: {
        Args: { p_team_id: string }
        Returns: {
          id: string
          title: string
          description: string
          status: string
          property_id: string
          property_name: string
          user_id: string
          user_email: string
          provider_id: string
          provider_name: string
          created_at: string
          updated_at: string
          platform: string
          team_id: string
        }[]
      }
      get_team_members: {
        Args: { p_team_id: string }
        Returns: {
          id: string
          team_id: string
          user_id: string
          added_by: string
          status: string
          created_at: string
          updated_at: string
          user_email: string
          user_name: string
          role: string
        }[]
      }
      get_team_members_with_profiles: {
        Args: { p_team_id: string }
        Returns: {
          added_by: string | null
          avatar_url: string | null
          created_at: string | null
          email: string | null
          first_name: string | null
          id: string | null
          is_super_admin: boolean | null
          last_name: string | null
          profile_role: Database["public"]["Enums"]["user_role"] | null
          status: string | null
          team_id: string | null
          updated_at: string | null
          user_id: string | null
        }[]
      }
      get_team_name_by_id: {
        Args: { team_id: string }
        Returns: string
      }
      get_team_properties: {
        Args: { p_team_id: string }
        Returns: string[]
      }
      get_unique_user_properties: {
        Args: { p_user_id: string }
        Returns: {
          id: string
          name: string
          address: string
          city: string
          state: string
          user_id: string
          team_id: string
          created_at: string
          updated_at: string
        }[]
      }
      get_user_accessible_properties: {
        Args: { p_user_id: string }
        Returns: {
          address: string
          bathrooms: number | null
          bedrooms: number | null
          budget: number | null
          check_in_time: string | null
          check_out_time: string | null
          city: string
          collections: Json | null
          created_at: string
          current_checkout: string | null
          ical_url: string | null
          id: string
          image_url: string | null
          is_occupied: boolean | null
          last_ical_sync: string | null
          name: string
          next_booking: string | null
          next_checkin_date: string | null
          next_checkin_formatted: string | null
          state: string
          team_id: string | null
          timezone: string | null
          updated_at: string
          user_id: string
          zip: string
        }[]
      }
      get_user_damage_photos: {
        Args: { p_user_id: string }
        Returns: {
          id: string
          damage_report_id: string
          file_name: string
          file_path: string
          caption: string
          user_id: string
          created_at: string
          updated_at: string
          media_type: string
          public_url: string
        }[]
      }
      get_user_damage_reports: {
        Args: Record<PropertyKey, never> | { p_user_id: string }
        Returns: {
          created_at: string
          description: string
          id: string
          platform: string | null
          property_id: string
          provider_id: string | null
          status: string
          team_id: string | null
          title: string
          updated_at: string
          user_id: string
        }[]
      }
      get_user_damage_reports_simple: {
        Args: { p_user_id: string }
        Returns: {
          id: string
          title: string
          description: string
          status: string
          property_id: string
          property_name: string
          user_id: string
          created_at: string
          updated_at: string
          provider_id: string
          provider_name: string
          platform: string
          team_id: string
        }[]
      }
      get_user_inventory_items: {
        Args: { p_user_id: string }
        Returns: {
          id: string
          name: string
          property_id: string
          property_name: string
          collection: string
          quantity: number
          min_quantity: number
          price: number
          amazon_url: string
          walmart_url: string
          image_url: string
          last_ordered: string
          user_id: string
          team_id: string
          created_at: string
          updated_at: string
        }[]
      }
      get_user_maintenance_tasks: {
        Args: { p_user_id: string }
        Returns: {
          assigned_to: string | null
          completed_at: string | null
          created_at: string
          description: string | null
          due_date: string | null
          email_notification_sent: boolean | null
          email_notification_sent_at: string | null
          id: string
          is_recurring: boolean | null
          max_recurrences: number | null
          next_due_date: string | null
          parent_task_id: string | null
          property_id: string | null
          property_name: string
          provider_email: string | null
          provider_id: string | null
          recurrence_count: number | null
          recurrence_interval_days: number | null
          severity: string
          status: string
          team_id: string | null
          title: string
          updated_at: string
          user_id: string
        }[]
      }
      get_user_properties: {
        Args: { p_user_id: string }
        Returns: {
          address: string
          bathrooms: number | null
          bedrooms: number | null
          budget: number | null
          check_in_time: string | null
          check_out_time: string | null
          city: string
          collections: Json | null
          created_at: string
          current_checkout: string | null
          ical_url: string | null
          id: string
          image_url: string | null
          is_occupied: boolean | null
          last_ical_sync: string | null
          name: string
          next_booking: string | null
          next_checkin_date: string | null
          next_checkin_formatted: string | null
          state: string
          team_id: string | null
          timezone: string | null
          updated_at: string
          user_id: string
          zip: string
        }[]
      }
      get_user_property_by_name: {
        Args: { p_user_id: string; p_property_name: string }
        Returns: {
          id: string
          name: string
        }[]
      }
      get_user_role: {
        Args: { user_id: string }
        Returns: string
      }
      get_user_role_properties: {
        Args: { p_user_id: string }
        Returns: {
          address: string
          bathrooms: number | null
          bedrooms: number | null
          budget: number | null
          check_in_time: string | null
          check_out_time: string | null
          city: string
          collections: Json | null
          created_at: string
          current_checkout: string | null
          ical_url: string | null
          id: string
          image_url: string | null
          is_occupied: boolean | null
          last_ical_sync: string | null
          name: string
          next_booking: string | null
          next_checkin_date: string | null
          next_checkin_formatted: string | null
          state: string
          team_id: string | null
          timezone: string | null
          updated_at: string
          user_id: string
          zip: string
        }[]
      }
      get_user_role_sd: {
        Args: { user_id: string }
        Returns: string
      }
      get_user_team_access: {
        Args: { team_id: string }
        Returns: boolean
      }
      get_user_team_ids: {
        Args: Record<PropertyKey, never>
        Returns: string[]
      }
      get_user_team_memberships: {
        Args: { p_user_id: string }
        Returns: {
          team_id: string
        }[]
      }
      get_user_teams: {
        Args: { user_id: string }
        Returns: string[]
      }
      has_damage_note_access: {
        Args: { p_note_id: string }
        Returns: boolean
      }
      has_damage_photo_access: {
        Args: { p_photo_id: string }
        Returns: boolean
      }
      has_damage_report_access: {
        Args: { p_report_id: string }
        Returns: boolean
      }
      has_inventory_item_access: {
        Args: { p_item_id: string }
        Returns: boolean
      }
      has_maintenance_provider_access: {
        Args: { p_provider_id: string }
        Returns: boolean
      }
      has_maintenance_task_access: {
        Args: { task_id: string }
        Returns: boolean
      }
      has_maintenance_task_access_impersonation: {
        Args: { p_task_id: string }
        Returns: boolean
      }
      has_permission: {
        Args: {
          user_id: string
          permission_name: Database["public"]["Enums"]["permission_type"]
          team_id?: string
        }
        Returns: boolean
      }
      has_property_access: {
        Args: { p_property_id: string }
        Returns: boolean
      }
      has_property_document_access: {
        Args: { p_document_id: string }
        Returns: boolean
      }
      has_property_file_access: {
        Args: { p_file_id: string }
        Returns: boolean
      }
      has_purchase_order_access: {
        Args: { p_order_id: string }
        Returns: boolean
      }
      has_purchase_order_item_access: {
        Args: { p_item_id: string }
        Returns: boolean
      }
      has_service_provider_access: {
        Args: { p_provider_id: string }
        Returns: boolean
      }
      has_user_access: {
        Args: { p_user_id: string }
        Returns: boolean
      }
      is_admin_user: {
        Args: { user_id: string }
        Returns: boolean
      }
      is_current_user_super_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_property_in_team: {
        Args: { p_property_id: string; p_team_id: string }
        Returns: boolean
      }
      is_service_provider_in_team: {
        Args: { p_property_manager_id: string; p_service_provider_id: string }
        Returns: boolean
      }
      is_super_admin: {
        Args: { uid: string }
        Returns: boolean
      }
      is_super_admin_sd: {
        Args: { user_id: string }
        Returns: boolean
      }
      is_team_member: {
        Args: { team_id: string; user_id: string }
        Returns: boolean
      }
      is_team_member_sd: {
        Args: { team_id: string; user_id: string }
        Returns: boolean
      }
      is_team_member_with_property_access: {
        Args: { p_user_id: string; p_property_id: string }
        Returns: boolean
      }
      is_team_owner: {
        Args: { team_id: string; user_id: string }
        Returns: boolean
      }
      is_team_owner_sd: {
        Args: { team_id: string; user_id: string }
        Returns: boolean
      }
      process_invitation_acceptance: {
        Args: { p_token: string; p_user_id: string }
        Returns: Json
      }
      property_belongs_to_team: {
        Args: { p_property_id: string; p_team_id: string }
        Returns: boolean
      }
      register_service_provider: {
        Args: {
          p_email: string
          p_password: string
          p_first_name: string
          p_last_name: string
          p_invitation_token?: string
        }
        Returns: Json
      }
      remove_property_from_team_direct: {
        Args: { p_team_id: string; p_property_id: string; p_user_id: string }
        Returns: boolean
      }
      remove_property_from_team_safe: {
        Args: { p_team_id: string; p_property_id: string; p_user_id: string }
        Returns: boolean
      }
      run_sql: {
        Args: { sql: string }
        Returns: undefined
      }
      scheduled_ensure_profiles: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      set_storage_policy: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      sync_inventory_team_ids: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      update_user_preferences: {
        Args: { p_user_id: string; p_onboarding_state: Json }
        Returns: boolean
      }
      user_can_access_team: {
        Args: { target_team_id: string } | { user_id: string; team_id: string }
        Returns: boolean
      }
      user_can_access_team_members: {
        Args: { target_team_id: string }
        Returns: boolean
      }
      user_can_access_team_properties: {
        Args: { target_team_id: string }
        Returns: boolean
      }
      user_can_access_team_sd: {
        Args: { user_id: string; team_id: string }
        Returns: boolean
      }
      user_has_permission: {
        Args: { p_user_id: string; p_permission: string }
        Returns: boolean
      }
      user_has_team_permission: {
        Args: { p_user_id: string; p_team_id: string; p_permission: string }
        Returns: boolean
      }
      user_is_team_member: {
        Args: { team_id: string }
        Returns: boolean
      }
      user_is_team_owner: {
        Args: { team_id: string }
        Returns: boolean
      }
      user_owns_team: {
        Args: { team_id: string }
        Returns: boolean
      }
      verify_backup: {
        Args: { backup_id: string }
        Returns: {
          is_valid: boolean
          tables_count: number
          rows_count: number
          issues: string[]
        }[]
      }
    }
    Enums: {
      permission_type:
        | "manage_properties"
        | "submit_damage_reports"
        | "manage_inventory"
        | "view_inventory"
        | "manage_staff"
        | "manage_service_providers"
        | "view_reports"
        | "edit_reports"
        | "admin_dashboard_access"
        | "impersonate_users"
        | "edit_user_data"
        | "add_users"
        | "delete_users"
        | "manage_subscriptions"
        | "admin"
        | "manage_purchase_orders"
        | "view_purchase_orders"
        | "manage_damage_reports"
        | "view_damage_reports"
        | "manage_maintenance"
        | "view_maintenance"
        | "manage_team"
        | "view_team"
        | "create_maintenance_task"
      po_status: "pending" | "ordered" | "delivered" | "archived"
      user_role:
        | "super_admin"
        | "admin"
        | "property_manager"
        | "staff"
        | "service_provider"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      permission_type: [
        "manage_properties",
        "submit_damage_reports",
        "manage_inventory",
        "view_inventory",
        "manage_staff",
        "manage_service_providers",
        "view_reports",
        "edit_reports",
        "admin_dashboard_access",
        "impersonate_users",
        "edit_user_data",
        "add_users",
        "delete_users",
        "manage_subscriptions",
        "admin",
        "manage_purchase_orders",
        "view_purchase_orders",
        "manage_damage_reports",
        "view_damage_reports",
        "manage_maintenance",
        "view_maintenance",
        "manage_team",
        "view_team",
        "create_maintenance_task",
      ],
      po_status: ["pending", "ordered", "delivered", "archived"],
      user_role: [
        "super_admin",
        "admin",
        "property_manager",
        "staff",
        "service_provider",
      ],
    },
  },
} as const
