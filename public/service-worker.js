
// Service Worker for StayFu PWA
const CACHE_NAME = 'stayfu-cache-v8'; // Increment the cache version
const APP_SHELL_CACHE = 'stayfu-app-shell-v8';
const DYNAMIC_CACHE = 'stayfu-dynamic-v8';
const API_CACHE = 'stayfu-api-v8';

// Assets to cache on install (app shell)
const APP_SHELL_ASSETS = [
  '/',
  '/index.html',
  '/offline.html',
  '/manifest.json',
  '/#/',
  '/#/dashboard',
  '/#/login',
  '/icons/favicon.ico',
  '/icons/apple-touch-icon.png',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  '/icons/logo.png'
];

// Helper function to clean up old caches
const clearOldCaches = async () => {
  const cacheKeepList = [CACHE_NAME, APP_SHELL_CACHE, DYNAMIC_CACHE, API_CACHE];
  const keyList = await caches.keys();

  return Promise.all(
    keyList.map(key => {
      if (!cacheKeepList.includes(key)) {
        console.log('[Service Worker] Removing old cache', key);
        return caches.delete(key);
      }
    })
  );
};

// Helper function to determine if a request is for a page navigation
const isNavigationRequest = (request) => {
  return (
    request.mode === 'navigate' ||
    (request.method === 'GET' &&
      request.headers.get('accept')?.includes('text/html'))
  );
};

// Helper function to determine if a URL is an SPA route
const isSpaRoute = (url) => {
  // Get the pathname from the URL
  const urlObj = new URL(url);
  const path = urlObj.pathname;

  // Check if it's a hash route or a direct route
  return path === '/' ||
         path.startsWith('/#/') ||
         path.includes('/dashboard') ||
         path.includes('/properties') ||
         path.includes('/maintenance') ||
         path.includes('/inventory') ||
         path.includes('/settings') ||
         path.includes('/login') ||
         path.includes('/register') ||
         path.includes('/teams') ||
         path.includes('/damages') ||
         path.includes('/purchase-orders') ||
         path.includes('/collections') ||
         path.includes('/invoices') ||
         path.includes('/admin') ||
         path.includes('/style-guide');
};

// Helper function to determine if a request is for an API call
const isApiRequest = (request) => {
  return request.url.includes('/rest/v1/') ||
         request.url.includes('/auth/v1/') ||
         request.url.includes('/functions/v1/');
};

// Helper function to determine if the request is an authentication endpoint
const isAuthRequest = (request) => {
  return request.url.includes('/auth/v1/token') ||
         request.url.includes('/auth/v1/logout') ||
         request.url.includes('/auth/v1/signup');
};

// Helper function to determine if the request is for the manifest.json file
const isManifestRequest = (request) => {
  return request.url.endsWith('/manifest.json');
};

// Install event - cache the app shell
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Installing Service Worker');

  event.waitUntil(
    Promise.all([
      // Cache app shell assets
      caches.open(APP_SHELL_CACHE)
        .then((cache) => {
          console.log('[Service Worker] Caching App Shell');
          // Use a more robust approach to cache assets
          return Promise.all(
            APP_SHELL_ASSETS.map(url => {
              return fetch(url, { cache: 'no-cache' })
                .then(response => {
                  if (!response || response.status !== 200) {
                    console.error(`[Service Worker] Failed to cache: ${url}`);
                    return;
                  }
                  return cache.put(url, response);
                })
                .catch(error => {
                  console.error(`[Service Worker] Error caching ${url}:`, error);
                });
            })
          );
        })
        .catch(error => {
          console.error('[Service Worker] Error during app shell caching:', error);
        }),
      // Skip waiting to activate immediately
      self.skipWaiting()
    ])
  );
});

// Activate event - clean up old caches and claim clients
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Activating Service Worker');

  event.waitUntil(
    Promise.all([
      clearOldCaches(),
      self.clients.claim() // Take control of all clients
    ])
  );
});

// Fetch event - different strategies for different types of requests
self.addEventListener('fetch', (event) => {
  // Skip Supabase auth requests to ensure they always go to the network
  if (isAuthRequest(event.request)) {
    return;
  }

  // Handle manifest.json requests specially to avoid 401 errors
  if (isManifestRequest(event.request)) {
    event.respondWith(
      caches.match('/manifest.json')
        .then((cachedResponse) => {
          if (cachedResponse) {
            return cachedResponse;
          }

          // Try to fetch from network with same-origin enforcement
          const manifestUrl = new URL('/manifest.json', self.location.origin);
          return fetch(manifestUrl)
            .then((networkResponse) => {
              if (networkResponse && networkResponse.ok) {
                const clonedResponse = networkResponse.clone();
                caches.open(APP_SHELL_CACHE).then((cache) => {
                  cache.put('/manifest.json', clonedResponse);
                });
                return networkResponse;
              }
              throw new Error('Failed to fetch manifest.json');
            })
            .catch((error) => {
              console.error('[Service Worker] Manifest fetch error:', error);
              // Return an empty but valid manifest as fallback
              return new Response(JSON.stringify({
                name: "StayFu",
                short_name: "StayFu",
                start_url: "/#/dashboard",
                display: "standalone",
                background_color: "#ffffff",
                theme_color: "#ffffff",
                icons: []
              }), {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
              });
            });
        })
    );
    return;
  }

  // Handle cross-origin requests
  if (!event.request.url.startsWith(self.location.origin)) {
    // If it's a manifest.json request from another origin, redirect to our local manifest
    if (event.request.url.endsWith('/manifest.json')) {
      event.respondWith(
        caches.match('/manifest.json')
          .then(response => response || fetch('/manifest.json'))
      );
      return;
    }

    // For other cross-origin requests, let the browser handle them normally
    // except for Supabase which we want to handle
    if (!event.request.url.includes('supabase.co')) {
      return;
    }
  }

  // Handle navigation requests (HTML pages)
  if (isNavigationRequest(event.request)) {
    event.respondWith(
      caches.match(event.request)
        .then((cachedResponse) => {
          // Try to fetch from network
          return fetch(event.request)
            .then((networkResponse) => {
              // Cache the updated page
              if (networkResponse && networkResponse.ok) {
                const clonedResponse = networkResponse.clone();
                caches.open(APP_SHELL_CACHE).then((cache) => {
                  cache.put(event.request, clonedResponse);
                });
              }
              return networkResponse;
            })
            .catch(() => {
              // If network fails, serve from cache
              if (cachedResponse) {
                return cachedResponse;
              }

              // If it's an SPA route, serve the index.html
              if (isSpaRoute(event.request.url)) {
                return caches.match('/index.html');
              }

              // Fall back to offline page
              return caches.match('/offline.html');
            });
        })
    );
    return;
  }

  // Handle API requests - Network first, then cache (only for GET requests)
  if (isApiRequest(event.request)) {
    // Only handle GET requests for caching
    if (event.request.method !== 'GET') {
      // For non-GET requests (POST, PUT, DELETE, etc.), just pass through to network
      return;
    }

    event.respondWith(
      fetch(event.request.clone())
        .then((response) => {
          // Only cache successful responses
          if (response.ok && response.status === 200) {
            const clonedResponse = response.clone();
            caches.open(API_CACHE).then((cache) => {
              cache.put(event.request, clonedResponse);
            });
          }
          return response;
        })
        .catch(() => {
          // If network fails, try to serve from cache
          return caches.match(event.request)
            .then((cachedResponse) => {
              if (cachedResponse) {
                return cachedResponse;
              }
              // If no cached response, let the error propagate to the client
              throw new Error('No cached response available for API request');
            });
        })
    );
    return;
  }

  // Handle asset requests (JS, CSS, images) - Cache first strategy
  // Only handle GET requests for caching
  if (event.request.method !== 'GET') {
    // For non-GET requests, just pass through to network
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then((cachedResponse) => {
        if (cachedResponse) {
          // Return cached response immediately
          // But also update cache in background for next time
          fetch(event.request)
            .then((networkResponse) => {
              if (networkResponse && networkResponse.ok) {
                caches.open(DYNAMIC_CACHE).then((cache) => {
                  cache.put(event.request, networkResponse.clone());
                });
              }
            })
            .catch(() => console.log('[Service Worker] Update failed, using cached version'));

          return cachedResponse;
        }

        // No cache hit, try network
        return fetch(event.request)
          .then((response) => {
            if (!response || !response.ok) {
              return response;
            }

            // Clone the response
            const responseToCache = response.clone();

            // Cache the fetched response for future
            caches.open(DYNAMIC_CACHE)
              .then((cache) => {
                cache.put(event.request, responseToCache);
              });

            return response;
          })
          .catch((error) => {
            console.log(`[Service Worker] Network fetch error for ${event.request.url}:`, error);

            // If it's an image request, return a fallback image
            if (event.request.url.match(/\.(jpg|jpeg|png|gif|svg)$/)) {
              return caches.match('/icons/logo.png')
                .then(response => {
                  if (response) {
                    return response;
                  }
                  // If even the fallback image is not available, return a simple transparent image
                  return new Response(
                    new Blob([
                      'R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'
                    ], { type: 'image/gif' }),
                    { status: 200 }
                  );
                });
            }

            // For JS/CSS resources, try to return an empty but valid response
            if (event.request.url.match(/\.(js|css)$/)) {
              const contentType = event.request.url.endsWith('.js') ? 'application/javascript' : 'text/css';
              return new Response('/* Empty fallback */', {
                status: 200,
                headers: { 'Content-Type': contentType }
              });
            }

            // For other resources, return a generic error response
            return new Response('Network error occurred', {
              status: 503,
              headers: { 'Content-Type': 'text/plain' }
            });
          });
      })
  );
});

// Add a message listener to help with debugging and handle version updates
self.addEventListener('message', (event) => {
  console.log('[Service Worker] Message received:', event.data);

  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }

  // Check if we need to get a fresh version from the network
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    clearOldCaches().then(() => {
      // Send confirmation back to the client
      if (event.ports && event.ports[0]) {
        event.ports[0].postMessage({
          type: 'CACHE_CLEARED'
        });
      }
    });
  }

  // For debugging purposes
  if (event.data && event.data.type === 'GET_VERSION') {
    if (event.ports && event.ports[0]) {
      event.ports[0].postMessage({
        version: CACHE_NAME
      });
    }
  }

  // Clear specific URLs from cache
  if (event.data && event.data.type === 'CLEAR_URL' && event.data.url) {
    Promise.all([
      caches.open(APP_SHELL_CACHE),
      caches.open(DYNAMIC_CACHE),
      caches.open(API_CACHE)
    ]).then(([appShellCache, dynamicCache, apiCache]) => {
      appShellCache.delete(event.data.url);
      dynamicCache.delete(event.data.url);
      apiCache.delete(event.data.url);

      if (event.ports && event.ports[0]) {
        event.ports[0].postMessage({
          type: 'URL_CLEARED',
          url: event.data.url
        });
      }
    });
  }
});

// Push notification listener
self.addEventListener('push', (event) => {
  const data = event.data ? event.data.json() : {};
  const title = data.title || 'StayFu Notification';
  const options = {
    body: data.body || 'Something requires your attention',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/logo.png',
    data: data.url || '/',
    actions: data.actions || []
  };

  event.waitUntil(
    self.registration.showNotification(title, options)
  );
});

// Notification click handler
self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  // Handle action clicks
  if (event.action) {
    console.log(`[Service Worker] Action clicked: ${event.action}`);
    // Custom logic for different actions
  }

  // Get target URL from notification data
  const targetUrl = event.notification.data || '/';

  event.waitUntil(
    clients.matchAll({type: 'window'})
      .then((clientList) => {
        // Check if there's already a window/tab open with the target URL
        for (const client of clientList) {
          if (client.url === targetUrl && 'focus' in client) {
            return client.focus();
          }
        }
        // If no window/tab is open, open a new one
        if (clients.openWindow) {
          return clients.openWindow(targetUrl);
        }
      })
  );
});
